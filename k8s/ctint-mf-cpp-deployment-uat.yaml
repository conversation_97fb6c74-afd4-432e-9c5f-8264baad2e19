apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-cpp-deployment
  namespace: cdss-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-cpp
  template:
    metadata:
      labels:
        app: ctint-mf-cpp
    spec:
      containers:
      - name: ctint-mf-cpp
        image: cdss3uatacr.azurecr.io/ctint-mf-cpp:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/public/config
      volumes:  
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3uatakssa-share
          shareName: ctint-cdss-globalconfig
          readOnly: false