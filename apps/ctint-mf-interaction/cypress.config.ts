import { nxE2EPreset } from '@nx/cypress/plugins/cypress-preset';

import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    ...nxE2EPreset(__filename, {
      cypressDir: 'cypress',
      webServerCommands: {
        default: 'nx run ctint-mf-interaction:serve --verbose',
      },
      ciWebServerCommand: 'nx run ctint-mf-interaction:serve-static',
    }),
    baseUrl: 'http://localhost:4900',
  },
});
