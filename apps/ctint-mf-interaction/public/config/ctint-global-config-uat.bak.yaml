auth:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
  - name: cdss_authorization
logger:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: mypurecloud.com.au
  apiHost: https://api.mypurecloud.com.au
  authHost: https://login.mypurecloud.com.au
loginTotal: 1
missingTotal: 3
poolTotal: 10000
heartbeatTime: 30
socketType: CDSS
recording:
  mediaSource:
  - aws
  - alicloud
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.cdss-data-ctint.svc.cluster.local:35000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-state-cdssmf-service.cdss-data-ctint.svc.cluster.local:35010
    basepath: /v1.0/state/ctint-state-cdssmf
    active: true
  session-manager:
    host: http://ctint-state-session-manager-service.cdss-data-ctint.svc.cluster.local:35020
    basepath: /v1.0/state/ctint-state-session-manager
    active: true
portals:
  - ctint-mf-interaction
services:
  ctint-stt:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/stt
    active: true
    provider:
      - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/auth
    active: true
    provider: # pure-engage / genesys-cloud / ctint-dab-auth / ctint-state-auth
      - pure-engage
      - ctint-dab-auth
      - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/conv
    active: true
    provider:
      - pureengage
      - ctint-dab-conv
    healthcheck: /healthcheck
  ctint-qm:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/qm
    active: true
    healthcheck: /healthcheck
  ctint-config:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/config
    active: true
    provider:
      - pureengage
      - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session
    active: true
    provider:
      - ctint-state-cdssmf
    healthcheck: /healthcheck
  ctint-session-manager:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session-manager
    active: true
    healthcheck: /healthcheck
  ctint-cdss-ws:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/cdss-ws
    active: true
    healthcheck: /healthcheck
  ctint-ccsp-ws:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/ccsp-ws
    active: true
    healthcheck: /healthcheck
  ctint-call-control:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/call-control
    active: true
    healthcheck: /healthcheck
  ctint-user:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/user
    active: true
    healthcheck: /healthcheck
microfrontends:
  ctint-mf-cdss:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-cdss
  ctint-mf-cpp:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-cpp
  ctint-mf-interaction:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-interaction
languages:
  supportedLanguages:
  - en
  - zh-HK
  defaultLanguage: en