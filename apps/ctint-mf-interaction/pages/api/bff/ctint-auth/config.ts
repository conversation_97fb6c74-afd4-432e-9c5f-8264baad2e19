import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const mfName = 'ctint-mf-interaction';
    const yamlEnv = process?.env?.CDSS_PUBLIC_ENVIRONMENT || 'build';
    let yamlPath = path.join(
      process.cwd(),
      `apps/${mfName}/public/config/ctint-global-config-${yamlEnv}.yaml`
    );
    if (!fs.existsSync(yamlPath)) {
      yamlPath = path.join(
        process.cwd(),
        `public/config/ctint-global-config-${yamlEnv}.yaml`
      );
      if (!fs.existsSync(yamlPath)) {
        throw new Error(`Configuration file not found: ${yamlPath}`);
      }
    }
    const fileContents = fs.readFileSync(yamlPath, 'utf8');
    const configData = yaml.load(fileContents);

    res.status(200).json({
      isSuccess: true,
      data: configData,
      error: null,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      isSuccess: false,
      error: 'Failed to load config',
    });
  }
}
