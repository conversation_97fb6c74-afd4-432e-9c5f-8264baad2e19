{"name": "ctint-mf-interaction", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-interaction", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-interaction", "outputPath": "dist/apps/ctint-mf-interaction"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-interaction:build", "dev": true, "port": 4900, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-interaction:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-interaction:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-interaction:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-interaction"], "options": {"jestConfig": "apps/ctint-mf-interaction/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-interaction/**/*.{ts,tsx,js,jsx}"]}}}}