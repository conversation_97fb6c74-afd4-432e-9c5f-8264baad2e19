export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
    };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  Byte: { input: any; output: any };
  ByteArray: { input: any; output: any };
  DateTime: { input: string; output: string };
  Decimal: { input: any; output: any };
  LocalTime: { input: any; output: any };
  Long: { input: number; output: number };
  Short: { input: any; output: any };
  Single: { input: any; output: any };
  UUID: { input: any; output: any };
};

export enum ApplyPolicy {
  AfterResolver = 'AFTER_RESOLVER',
  BeforeResolver = 'BEFORE_RESOLVER',
}

/** Input type for adding Boolean filters */
export type BooleanFilterInput = {
  /** Equals */
  eq: InputMaybe<Scalars['Boolean']['input']>;
  /** Not null test */
  isNull: InputMaybe<Scalars['Boolean']['input']>;
  /** Not Equals */
  neq: InputMaybe<Scalars['Boolean']['input']>;
};

/** Input type for creating address */
export type CreateaddressInput = {
  /** Input for field AddressLine1 on type CreateaddressInput */
  AddressLine1: Scalars['String']['input'];
  /** Input for field AddressLine2 on type CreateaddressInput */
  AddressLine2: InputMaybe<Scalars['String']['input']>;
  /** Input for field City on type CreateaddressInput */
  City: Scalars['String']['input'];
  /** Input for field CountryRegion on type CreateaddressInput */
  CountryRegion: Scalars['String']['input'];
  /** Input for field ModifiedDate on type CreateaddressInput */
  ModifiedDate: InputMaybe<Scalars['DateTime']['input']>;
  /** Input for field PostalCode on type CreateaddressInput */
  PostalCode: Scalars['String']['input'];
  /** Input for field StateProvince on type CreateaddressInput */
  StateProvince: Scalars['String']['input'];
  /** Input for field rowguid on type CreateaddressInput */
  rowguid: InputMaybe<Scalars['UUID']['input']>;
};

/** Input type for creating customerAddress */
export type CreatecustomerAddressInput = {
  /** Input for field AddressID on type CreatecustomerAddressInput */
  AddressID: Scalars['Int']['input'];
  /** Input for field AddressType on type CreatecustomerAddressInput */
  AddressType: Scalars['String']['input'];
  /** Input for field CustomerID on type CreatecustomerAddressInput */
  CustomerID: Scalars['Int']['input'];
  /** Input for field ModifiedDate on type CreatecustomerAddressInput */
  ModifiedDate: InputMaybe<Scalars['DateTime']['input']>;
  /** Input for field rowguid on type CreatecustomerAddressInput */
  rowguid: InputMaybe<Scalars['UUID']['input']>;
};

/** Input type for creating customerData */
export type CreatecustomerDataInput = {
  /** Input for field birthYear on type CreatecustomerDataInput */
  birthYear: InputMaybe<Scalars['Short']['input']>;
  /** Input for field deathYear on type CreatecustomerDataInput */
  deathYear: InputMaybe<Scalars['String']['input']>;
  /** Input for field knownForTitles on type CreatecustomerDataInput */
  knownForTitles: InputMaybe<Scalars['String']['input']>;
  /** Input for field nconst on type CreatecustomerDataInput */
  nconst: Scalars['String']['input'];
  /** Input for field primaryName on type CreatecustomerDataInput */
  primaryName: InputMaybe<Scalars['String']['input']>;
};

/** Input type for creating customerInfo */
export type CreatecustomerInfoInput = {
  /** Input for field birthYear on type CreatecustomerInfoInput */
  birthYear: InputMaybe<Scalars['Short']['input']>;
  /** Input for field deathYear on type CreatecustomerInfoInput */
  deathYear: InputMaybe<Scalars['String']['input']>;
  /** Input for field knownForTitles on type CreatecustomerInfoInput */
  knownForTitles: InputMaybe<Scalars['String']['input']>;
  /** Input for field nconst on type CreatecustomerInfoInput */
  nconst: Scalars['String']['input'];
  /** Input for field primaryName on type CreatecustomerInfoInput */
  primaryName: InputMaybe<Scalars['String']['input']>;
};

/** Input type for creating customer */
export type CreatecustomerInput = {
  /** Input for field CompanyName on type CreatecustomerInput */
  CompanyName: InputMaybe<Scalars['String']['input']>;
  /** Input for field EmailAddress on type CreatecustomerInput */
  EmailAddress: InputMaybe<Scalars['String']['input']>;
  /** Input for field FirstName on type CreatecustomerInput */
  FirstName: Scalars['String']['input'];
  /** Input for field LastName on type CreatecustomerInput */
  LastName: Scalars['String']['input'];
  /** Input for field MiddleName on type CreatecustomerInput */
  MiddleName: InputMaybe<Scalars['String']['input']>;
  /** Input for field ModifiedDate on type CreatecustomerInput */
  ModifiedDate: InputMaybe<Scalars['DateTime']['input']>;
  /** Input for field NameStyle on type CreatecustomerInput */
  NameStyle: InputMaybe<Scalars['Boolean']['input']>;
  /** Input for field PasswordHash on type CreatecustomerInput */
  PasswordHash: Scalars['String']['input'];
  /** Input for field PasswordSalt on type CreatecustomerInput */
  PasswordSalt: Scalars['String']['input'];
  /** Input for field Phone on type CreatecustomerInput */
  Phone: InputMaybe<Scalars['String']['input']>;
  /** Input for field SalesPerson on type CreatecustomerInput */
  SalesPerson: InputMaybe<Scalars['String']['input']>;
  /** Input for field Suffix on type CreatecustomerInput */
  Suffix: InputMaybe<Scalars['String']['input']>;
  /** Input for field Title on type CreatecustomerInput */
  Title: InputMaybe<Scalars['String']['input']>;
  /** Input for field rowguid on type CreatecustomerInput */
  rowguid: InputMaybe<Scalars['UUID']['input']>;
};

/** Input type for adding DateTime filters */
export type DateTimeFilterInput = {
  /** Equals */
  eq: InputMaybe<Scalars['DateTime']['input']>;
  /** Greater Than */
  gt: InputMaybe<Scalars['DateTime']['input']>;
  /** Greater Than or Equal To */
  gte: InputMaybe<Scalars['DateTime']['input']>;
  /** Not null test */
  isNull: InputMaybe<Scalars['Boolean']['input']>;
  /** Less Than */
  lt: InputMaybe<Scalars['DateTime']['input']>;
  /** Less Than or Equal To */
  lte: InputMaybe<Scalars['DateTime']['input']>;
  /** Not Equals */
  neq: InputMaybe<Scalars['DateTime']['input']>;
};

export type DefaultValue = {
  Boolean: InputMaybe<Scalars['Boolean']['input']>;
  Byte: InputMaybe<Scalars['Byte']['input']>;
  ByteArray: InputMaybe<Scalars['ByteArray']['input']>;
  DateTime: InputMaybe<Scalars['DateTime']['input']>;
  Decimal: InputMaybe<Scalars['Decimal']['input']>;
  Float: InputMaybe<Scalars['Float']['input']>;
  Int: InputMaybe<Scalars['Int']['input']>;
  LocalTime: InputMaybe<Scalars['LocalTime']['input']>;
  Long: InputMaybe<Scalars['Long']['input']>;
  Short: InputMaybe<Scalars['Short']['input']>;
  Single: InputMaybe<Scalars['Single']['input']>;
  String: InputMaybe<Scalars['String']['input']>;
};

/** Input type for adding Int filters */
export type IntFilterInput = {
  /** Equals */
  eq: InputMaybe<Scalars['Int']['input']>;
  /** Greater Than */
  gt: InputMaybe<Scalars['Int']['input']>;
  /** Greater Than or Equal To */
  gte: InputMaybe<Scalars['Int']['input']>;
  /** Not null test */
  isNull: InputMaybe<Scalars['Boolean']['input']>;
  /** Less Than */
  lt: InputMaybe<Scalars['Int']['input']>;
  /** Less Than or Equal To */
  lte: InputMaybe<Scalars['Int']['input']>;
  /** Not Equals */
  neq: InputMaybe<Scalars['Int']['input']>;
};

export type Mutation = {
  /** Creates a new address */
  createaddress: Maybe<Address>;
  /** Creates a new customer */
  createcustomer: Maybe<Customer>;
  /** Creates a new customerAddress */
  createcustomerAddress: Maybe<CustomerAddress>;
  /** Creates a new customerData */
  createcustomerData: Maybe<CustomerData>;
  /** Creates a new customerInfo */
  createcustomerInfo: Maybe<CustomerInfo>;
  /** Delete a address */
  deleteaddress: Maybe<Address>;
  /** Delete a customer */
  deletecustomer: Maybe<Customer>;
  /** Delete a customerAddress */
  deletecustomerAddress: Maybe<CustomerAddress>;
  /** Delete a customerData */
  deletecustomerData: Maybe<CustomerData>;
  /** Delete a customerInfo */
  deletecustomerInfo: Maybe<CustomerInfo>;
  /** Updates a address */
  updateaddress: Maybe<Address>;
  /** Updates a customer */
  updatecustomer: Maybe<Customer>;
  /** Updates a customerAddress */
  updatecustomerAddress: Maybe<CustomerAddress>;
  /** Updates a customerData */
  updatecustomerData: Maybe<CustomerData>;
  /** Updates a customerInfo */
  updatecustomerInfo: Maybe<CustomerInfo>;
};

export type MutationCreateaddressArgs = {
  item: CreateaddressInput;
};

export type MutationCreatecustomerArgs = {
  item: CreatecustomerInput;
};

export type MutationCreatecustomerAddressArgs = {
  item: CreatecustomerAddressInput;
};

export type MutationCreatecustomerDataArgs = {
  item: CreatecustomerDataInput;
};

export type MutationCreatecustomerInfoArgs = {
  item: CreatecustomerInfoInput;
};

export type MutationDeleteaddressArgs = {
  AddressID: Scalars['Int']['input'];
};

export type MutationDeletecustomerArgs = {
  CustomerID: Scalars['Int']['input'];
};

export type MutationDeletecustomerAddressArgs = {
  AddressID: Scalars['Int']['input'];
  CustomerID: Scalars['Int']['input'];
};

export type MutationDeletecustomerDataArgs = {
  nconst: Scalars['String']['input'];
};

export type MutationDeletecustomerInfoArgs = {
  nconst: Scalars['String']['input'];
};

export type MutationUpdateaddressArgs = {
  AddressID: Scalars['Int']['input'];
  item: UpdateaddressInput;
};

export type MutationUpdatecustomerArgs = {
  CustomerID: Scalars['Int']['input'];
  item: UpdatecustomerInput;
};

export type MutationUpdatecustomerAddressArgs = {
  AddressID: Scalars['Int']['input'];
  CustomerID: Scalars['Int']['input'];
  item: UpdatecustomerAddressInput;
};

export type MutationUpdatecustomerDataArgs = {
  item: UpdatecustomerDataInput;
  nconst: Scalars['String']['input'];
};

export type MutationUpdatecustomerInfoArgs = {
  item: UpdatecustomerInfoInput;
  nconst: Scalars['String']['input'];
};

export enum OrderBy {
  Asc = 'ASC',
  Desc = 'DESC',
}

export type Query = {
  /** Get a address from the database by its ID/primary key */
  address_by_pk: Maybe<Address>;
  /** Get a list of all the address items from the database */
  addresses: AddressConnection;
  /** Get a customerAddress from the database by its ID/primary key */
  customerAddress_by_pk: Maybe<CustomerAddress>;
  /** Get a list of all the customerAddress items from the database */
  customerAddresses: CustomerAddressConnection;
  /** Get a customerData from the database by its ID/primary key */
  customerData_by_pk: Maybe<CustomerData>;
  /** Get a list of all the customerData items from the database */
  customerDatas: CustomerDataConnection;
  /** Get a customerInfo from the database by its ID/primary key */
  customerInfo_by_pk: Maybe<CustomerInfo>;
  /** Get a list of all the customerInfo items from the database */
  customerInfos: CustomerInfoConnection;
  /** Get a customer from the database by its ID/primary key */
  customer_by_pk: Maybe<Customer>;
  /** Get a list of all the customer items from the database */
  customers: CustomerConnection;
};

export type QueryAddress_By_PkArgs = {
  AddressID: Scalars['Int']['input'];
};

export type QueryAddressesArgs = {
  after: InputMaybe<Scalars['String']['input']>;
  filter: InputMaybe<AddressFilterInput>;
  first: InputMaybe<Scalars['Int']['input']>;
  orderBy: InputMaybe<AddressOrderByInput>;
};

export type QueryCustomerAddress_By_PkArgs = {
  AddressID: Scalars['Int']['input'];
  CustomerID: Scalars['Int']['input'];
};

export type QueryCustomerAddressesArgs = {
  after: InputMaybe<Scalars['String']['input']>;
  filter: InputMaybe<CustomerAddressFilterInput>;
  first: InputMaybe<Scalars['Int']['input']>;
  orderBy: InputMaybe<CustomerAddressOrderByInput>;
};

export type QueryCustomerData_By_PkArgs = {
  nconst: Scalars['String']['input'];
};

export type QueryCustomerDatasArgs = {
  after: InputMaybe<Scalars['String']['input']>;
  filter: InputMaybe<CustomerDataFilterInput>;
  first: InputMaybe<Scalars['Int']['input']>;
  orderBy: InputMaybe<CustomerDataOrderByInput>;
};

export type QueryCustomerInfo_By_PkArgs = {
  nconst: Scalars['String']['input'];
};

export type QueryCustomerInfosArgs = {
  after: InputMaybe<Scalars['String']['input']>;
  filter: InputMaybe<CustomerInfoFilterInput>;
  first: InputMaybe<Scalars['Int']['input']>;
  orderBy: InputMaybe<CustomerInfoOrderByInput>;
};

export type QueryCustomer_By_PkArgs = {
  CustomerID: Scalars['Int']['input'];
};

export type QueryCustomersArgs = {
  after: InputMaybe<Scalars['String']['input']>;
  filter: InputMaybe<CustomerFilterInput>;
  first: InputMaybe<Scalars['Int']['input']>;
  orderBy: InputMaybe<CustomerOrderByInput>;
};

/** Input type for adding Short filters */
export type ShortFilterInput = {
  /** Equals */
  eq: InputMaybe<Scalars['Short']['input']>;
  /** Greater Than */
  gt: InputMaybe<Scalars['Short']['input']>;
  /** Greater Than or Equal To */
  gte: InputMaybe<Scalars['Short']['input']>;
  /** Not null test */
  isNull: InputMaybe<Scalars['Boolean']['input']>;
  /** Less Than */
  lt: InputMaybe<Scalars['Short']['input']>;
  /** Less Than or Equal To */
  lte: InputMaybe<Scalars['Short']['input']>;
  /** Not Equals */
  neq: InputMaybe<Scalars['Short']['input']>;
};

/** Input type for adding String filters */
export type StringFilterInput = {
  /** Case Insensitive */
  caseInsensitive: InputMaybe<Scalars['Boolean']['input']>;
  /** Contains */
  contains: InputMaybe<Scalars['String']['input']>;
  /** Ends With */
  endsWith: InputMaybe<Scalars['String']['input']>;
  /** Equals */
  eq: InputMaybe<Scalars['String']['input']>;
  /** Is null test */
  isNull: InputMaybe<Scalars['Boolean']['input']>;
  /** Not Equals */
  neq: InputMaybe<Scalars['String']['input']>;
  /** Not Contains */
  notContains: InputMaybe<Scalars['String']['input']>;
  /** Starts With */
  startsWith: InputMaybe<Scalars['String']['input']>;
};

/** Input type for updating address */
export type UpdateaddressInput = {
  /** Input for field AddressLine1 on type UpdateaddressInput */
  AddressLine1: InputMaybe<Scalars['String']['input']>;
  /** Input for field AddressLine2 on type UpdateaddressInput */
  AddressLine2: InputMaybe<Scalars['String']['input']>;
  /** Input for field City on type UpdateaddressInput */
  City: InputMaybe<Scalars['String']['input']>;
  /** Input for field CountryRegion on type UpdateaddressInput */
  CountryRegion: InputMaybe<Scalars['String']['input']>;
  /** Input for field ModifiedDate on type UpdateaddressInput */
  ModifiedDate: InputMaybe<Scalars['DateTime']['input']>;
  /** Input for field PostalCode on type UpdateaddressInput */
  PostalCode: InputMaybe<Scalars['String']['input']>;
  /** Input for field StateProvince on type UpdateaddressInput */
  StateProvince: InputMaybe<Scalars['String']['input']>;
  /** Input for field rowguid on type UpdateaddressInput */
  rowguid: InputMaybe<Scalars['UUID']['input']>;
};

/** Input type for updating customerAddress */
export type UpdatecustomerAddressInput = {
  /** Input for field AddressID on type UpdatecustomerAddressInput */
  AddressID: InputMaybe<Scalars['Int']['input']>;
  /** Input for field AddressType on type UpdatecustomerAddressInput */
  AddressType: InputMaybe<Scalars['String']['input']>;
  /** Input for field CustomerID on type UpdatecustomerAddressInput */
  CustomerID: InputMaybe<Scalars['Int']['input']>;
  /** Input for field ModifiedDate on type UpdatecustomerAddressInput */
  ModifiedDate: InputMaybe<Scalars['DateTime']['input']>;
  /** Input for field rowguid on type UpdatecustomerAddressInput */
  rowguid: InputMaybe<Scalars['UUID']['input']>;
};

/** Input type for updating customerData */
export type UpdatecustomerDataInput = {
  /** Input for field birthYear on type UpdatecustomerDataInput */
  birthYear: InputMaybe<Scalars['Short']['input']>;
  /** Input for field deathYear on type UpdatecustomerDataInput */
  deathYear: InputMaybe<Scalars['String']['input']>;
  /** Input for field knownForTitles on type UpdatecustomerDataInput */
  knownForTitles: InputMaybe<Scalars['String']['input']>;
  /** Input for field nconst on type UpdatecustomerDataInput */
  nconst: InputMaybe<Scalars['String']['input']>;
  /** Input for field primaryName on type UpdatecustomerDataInput */
  primaryName: InputMaybe<Scalars['String']['input']>;
};

/** Input type for updating customerInfo */
export type UpdatecustomerInfoInput = {
  /** Input for field birthYear on type UpdatecustomerInfoInput */
  birthYear: InputMaybe<Scalars['Short']['input']>;
  /** Input for field deathYear on type UpdatecustomerInfoInput */
  deathYear: InputMaybe<Scalars['String']['input']>;
  /** Input for field knownForTitles on type UpdatecustomerInfoInput */
  knownForTitles: InputMaybe<Scalars['String']['input']>;
  /** Input for field nconst on type UpdatecustomerInfoInput */
  nconst: InputMaybe<Scalars['String']['input']>;
  /** Input for field primaryName on type UpdatecustomerInfoInput */
  primaryName: InputMaybe<Scalars['String']['input']>;
};

/** Input type for updating customer */
export type UpdatecustomerInput = {
  /** Input for field CompanyName on type UpdatecustomerInput */
  CompanyName: InputMaybe<Scalars['String']['input']>;
  /** Input for field EmailAddress on type UpdatecustomerInput */
  EmailAddress: InputMaybe<Scalars['String']['input']>;
  /** Input for field FirstName on type UpdatecustomerInput */
  FirstName: InputMaybe<Scalars['String']['input']>;
  /** Input for field LastName on type UpdatecustomerInput */
  LastName: InputMaybe<Scalars['String']['input']>;
  /** Input for field MiddleName on type UpdatecustomerInput */
  MiddleName: InputMaybe<Scalars['String']['input']>;
  /** Input for field ModifiedDate on type UpdatecustomerInput */
  ModifiedDate: InputMaybe<Scalars['DateTime']['input']>;
  /** Input for field NameStyle on type UpdatecustomerInput */
  NameStyle: InputMaybe<Scalars['Boolean']['input']>;
  /** Input for field PasswordHash on type UpdatecustomerInput */
  PasswordHash: InputMaybe<Scalars['String']['input']>;
  /** Input for field PasswordSalt on type UpdatecustomerInput */
  PasswordSalt: InputMaybe<Scalars['String']['input']>;
  /** Input for field Phone on type UpdatecustomerInput */
  Phone: InputMaybe<Scalars['String']['input']>;
  /** Input for field SalesPerson on type UpdatecustomerInput */
  SalesPerson: InputMaybe<Scalars['String']['input']>;
  /** Input for field Suffix on type UpdatecustomerInput */
  Suffix: InputMaybe<Scalars['String']['input']>;
  /** Input for field Title on type UpdatecustomerInput */
  Title: InputMaybe<Scalars['String']['input']>;
  /** Input for field rowguid on type UpdatecustomerInput */
  rowguid: InputMaybe<Scalars['UUID']['input']>;
};

/** Input type for adding Uuid filters */
export type UuidFilterInput = {
  /** Case Insensitive */
  caseInsensitive: InputMaybe<Scalars['Boolean']['input']>;
  /** Contains */
  contains: InputMaybe<Scalars['UUID']['input']>;
  /** Ends With */
  endsWith: InputMaybe<Scalars['UUID']['input']>;
  /** Equals */
  eq: InputMaybe<Scalars['UUID']['input']>;
  /** Is null test */
  isNull: InputMaybe<Scalars['Boolean']['input']>;
  /** Not Equals */
  neq: InputMaybe<Scalars['UUID']['input']>;
  /** Not Contains */
  notContains: InputMaybe<Scalars['UUID']['input']>;
  /** Starts With */
  startsWith: InputMaybe<Scalars['UUID']['input']>;
};

export type Address = {
  AddressID: Scalars['Int']['output'];
  AddressLine1: Scalars['String']['output'];
  AddressLine2: Maybe<Scalars['String']['output']>;
  City: Scalars['String']['output'];
  CountryRegion: Scalars['String']['output'];
  ModifiedDate: Scalars['DateTime']['output'];
  PostalCode: Scalars['String']['output'];
  StateProvince: Scalars['String']['output'];
  customers: CustomerAddressConnection;
  rowguid: Scalars['UUID']['output'];
};

export type AddressCustomersArgs = {
  after: InputMaybe<Scalars['String']['input']>;
  filter: InputMaybe<CustomerAddressFilterInput>;
  first: InputMaybe<Scalars['Int']['input']>;
  orderBy: InputMaybe<CustomerAddressOrderByInput>;
};

/** The return object from a filter query that supports a pagination token for paging through results */
export type AddressConnection = {
  /** A pagination token to provide to subsequent pages of a query */
  endCursor: Maybe<Scalars['String']['output']>;
  /** Indicates if there are more pages of items to return */
  hasNextPage: Scalars['Boolean']['output'];
  /** The list of items that matched the filter */
  items: Array<Address>;
};

/** Filter input for address GraphQL type */
export type AddressFilterInput = {
  /** Filter options for AddressID */
  AddressID: InputMaybe<IntFilterInput>;
  /** Filter options for AddressLine1 */
  AddressLine1: InputMaybe<StringFilterInput>;
  /** Filter options for AddressLine2 */
  AddressLine2: InputMaybe<StringFilterInput>;
  /** Filter options for City */
  City: InputMaybe<StringFilterInput>;
  /** Filter options for CountryRegion */
  CountryRegion: InputMaybe<StringFilterInput>;
  /** Filter options for ModifiedDate */
  ModifiedDate: InputMaybe<DateTimeFilterInput>;
  /** Filter options for PostalCode */
  PostalCode: InputMaybe<StringFilterInput>;
  /** Filter options for StateProvince */
  StateProvince: InputMaybe<StringFilterInput>;
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<AddressFilterInput>>>;
  /** Filter options for customers */
  customers: InputMaybe<CustomerAddressFilterInput>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<AddressFilterInput>>>;
  /** Filter options for rowguid */
  rowguid: InputMaybe<UuidFilterInput>;
};

/** Order by input for address GraphQL type */
export type AddressOrderByInput = {
  /** Order by options for AddressID */
  AddressID: InputMaybe<OrderBy>;
  /** Order by options for AddressLine1 */
  AddressLine1: InputMaybe<OrderBy>;
  /** Order by options for AddressLine2 */
  AddressLine2: InputMaybe<OrderBy>;
  /** Order by options for City */
  City: InputMaybe<OrderBy>;
  /** Order by options for CountryRegion */
  CountryRegion: InputMaybe<OrderBy>;
  /** Order by options for ModifiedDate */
  ModifiedDate: InputMaybe<OrderBy>;
  /** Order by options for PostalCode */
  PostalCode: InputMaybe<OrderBy>;
  /** Order by options for StateProvince */
  StateProvince: InputMaybe<OrderBy>;
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<AddressOrderByInput>>>;
  /** Order by options for customers */
  customers: InputMaybe<CustomerAddressOrderByInput>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<AddressOrderByInput>>>;
  /** Order by options for rowguid */
  rowguid: InputMaybe<OrderBy>;
};

export type Customer = {
  CompanyName: Maybe<Scalars['String']['output']>;
  CustomerID: Scalars['Int']['output'];
  EmailAddress: Maybe<Scalars['String']['output']>;
  FirstName: Scalars['String']['output'];
  LastName: Scalars['String']['output'];
  MiddleName: Maybe<Scalars['String']['output']>;
  ModifiedDate: Scalars['DateTime']['output'];
  NameStyle: Scalars['Boolean']['output'];
  PasswordHash: Scalars['String']['output'];
  PasswordSalt: Scalars['String']['output'];
  Phone: Maybe<Scalars['String']['output']>;
  SalesPerson: Maybe<Scalars['String']['output']>;
  Suffix: Maybe<Scalars['String']['output']>;
  Title: Maybe<Scalars['String']['output']>;
  addresses: CustomerAddressConnection;
  rowguid: Scalars['UUID']['output'];
};

export type CustomerAddressesArgs = {
  after: InputMaybe<Scalars['String']['input']>;
  filter: InputMaybe<CustomerAddressFilterInput>;
  first: InputMaybe<Scalars['Int']['input']>;
  orderBy: InputMaybe<CustomerAddressOrderByInput>;
};

export type CustomerAddress = {
  AddressID: Scalars['Int']['output'];
  AddressType: Scalars['String']['output'];
  CustomerID: Scalars['Int']['output'];
  ModifiedDate: Scalars['DateTime']['output'];
  rowguid: Scalars['UUID']['output'];
};

/** The return object from a filter query that supports a pagination token for paging through results */
export type CustomerAddressConnection = {
  /** A pagination token to provide to subsequent pages of a query */
  endCursor: Maybe<Scalars['String']['output']>;
  /** Indicates if there are more pages of items to return */
  hasNextPage: Scalars['Boolean']['output'];
  /** The list of items that matched the filter */
  items: Array<CustomerAddress>;
};

/** Filter input for customerAddress GraphQL type */
export type CustomerAddressFilterInput = {
  /** Filter options for AddressID */
  AddressID: InputMaybe<IntFilterInput>;
  /** Filter options for AddressType */
  AddressType: InputMaybe<StringFilterInput>;
  /** Filter options for CustomerID */
  CustomerID: InputMaybe<IntFilterInput>;
  /** Filter options for ModifiedDate */
  ModifiedDate: InputMaybe<DateTimeFilterInput>;
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<CustomerAddressFilterInput>>>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<CustomerAddressFilterInput>>>;
  /** Filter options for rowguid */
  rowguid: InputMaybe<UuidFilterInput>;
};

/** Order by input for customerAddress GraphQL type */
export type CustomerAddressOrderByInput = {
  /** Order by options for AddressID */
  AddressID: InputMaybe<OrderBy>;
  /** Order by options for AddressType */
  AddressType: InputMaybe<OrderBy>;
  /** Order by options for CustomerID */
  CustomerID: InputMaybe<OrderBy>;
  /** Order by options for ModifiedDate */
  ModifiedDate: InputMaybe<OrderBy>;
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<CustomerAddressOrderByInput>>>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<CustomerAddressOrderByInput>>>;
  /** Order by options for rowguid */
  rowguid: InputMaybe<OrderBy>;
};

/** The return object from a filter query that supports a pagination token for paging through results */
export type CustomerConnection = {
  /** A pagination token to provide to subsequent pages of a query */
  endCursor: Maybe<Scalars['String']['output']>;
  /** Indicates if there are more pages of items to return */
  hasNextPage: Scalars['Boolean']['output'];
  /** The list of items that matched the filter */
  items: Array<Customer>;
};

export type CustomerData = {
  birthYear: Maybe<Scalars['Short']['output']>;
  deathYear: Maybe<Scalars['String']['output']>;
  knownForTitles: Maybe<Scalars['String']['output']>;
  nconst: Scalars['String']['output'];
  primaryName: Maybe<Scalars['String']['output']>;
};

/** The return object from a filter query that supports a pagination token for paging through results */
export type CustomerDataConnection = {
  /** A pagination token to provide to subsequent pages of a query */
  endCursor: Maybe<Scalars['String']['output']>;
  /** Indicates if there are more pages of items to return */
  hasNextPage: Scalars['Boolean']['output'];
  /** The list of items that matched the filter */
  items: Array<CustomerData>;
};

/** Filter input for customerData GraphQL type */
export type CustomerDataFilterInput = {
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<CustomerDataFilterInput>>>;
  /** Filter options for birthYear */
  birthYear: InputMaybe<ShortFilterInput>;
  /** Filter options for deathYear */
  deathYear: InputMaybe<StringFilterInput>;
  /** Filter options for knownForTitles */
  knownForTitles: InputMaybe<StringFilterInput>;
  /** Filter options for nconst */
  nconst: InputMaybe<StringFilterInput>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<CustomerDataFilterInput>>>;
  /** Filter options for primaryName */
  primaryName: InputMaybe<StringFilterInput>;
};

/** Order by input for customerData GraphQL type */
export type CustomerDataOrderByInput = {
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<CustomerDataOrderByInput>>>;
  /** Order by options for birthYear */
  birthYear: InputMaybe<OrderBy>;
  /** Order by options for deathYear */
  deathYear: InputMaybe<OrderBy>;
  /** Order by options for knownForTitles */
  knownForTitles: InputMaybe<OrderBy>;
  /** Order by options for nconst */
  nconst: InputMaybe<OrderBy>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<CustomerDataOrderByInput>>>;
  /** Order by options for primaryName */
  primaryName: InputMaybe<OrderBy>;
};

/** Filter input for customer GraphQL type */
export type CustomerFilterInput = {
  /** Filter options for CompanyName */
  CompanyName: InputMaybe<StringFilterInput>;
  /** Filter options for CustomerID */
  CustomerID: InputMaybe<IntFilterInput>;
  /** Filter options for EmailAddress */
  EmailAddress: InputMaybe<StringFilterInput>;
  /** Filter options for FirstName */
  FirstName: InputMaybe<StringFilterInput>;
  /** Filter options for LastName */
  LastName: InputMaybe<StringFilterInput>;
  /** Filter options for MiddleName */
  MiddleName: InputMaybe<StringFilterInput>;
  /** Filter options for ModifiedDate */
  ModifiedDate: InputMaybe<DateTimeFilterInput>;
  /** Filter options for NameStyle */
  NameStyle: InputMaybe<BooleanFilterInput>;
  /** Filter options for PasswordHash */
  PasswordHash: InputMaybe<StringFilterInput>;
  /** Filter options for PasswordSalt */
  PasswordSalt: InputMaybe<StringFilterInput>;
  /** Filter options for Phone */
  Phone: InputMaybe<StringFilterInput>;
  /** Filter options for SalesPerson */
  SalesPerson: InputMaybe<StringFilterInput>;
  /** Filter options for Suffix */
  Suffix: InputMaybe<StringFilterInput>;
  /** Filter options for Title */
  Title: InputMaybe<StringFilterInput>;
  /** Filter options for addresses */
  addresses: InputMaybe<CustomerAddressFilterInput>;
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<CustomerFilterInput>>>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<CustomerFilterInput>>>;
  /** Filter options for rowguid */
  rowguid: InputMaybe<UuidFilterInput>;
};

export type CustomerInfo = {
  birthYear: Maybe<Scalars['Short']['output']>;
  customerInfoAndData: CustomerDataConnection;
  deathYear: Maybe<Scalars['String']['output']>;
  knownForTitles: Maybe<Scalars['String']['output']>;
  nconst: Scalars['String']['output'];
  primaryName: Maybe<Scalars['String']['output']>;
};

export type CustomerInfoCustomerInfoAndDataArgs = {
  after: InputMaybe<Scalars['String']['input']>;
  filter: InputMaybe<CustomerDataFilterInput>;
  first: InputMaybe<Scalars['Int']['input']>;
  orderBy: InputMaybe<CustomerDataOrderByInput>;
};

/** The return object from a filter query that supports a pagination token for paging through results */
export type CustomerInfoConnection = {
  /** A pagination token to provide to subsequent pages of a query */
  endCursor: Maybe<Scalars['String']['output']>;
  /** Indicates if there are more pages of items to return */
  hasNextPage: Scalars['Boolean']['output'];
  /** The list of items that matched the filter */
  items: Array<CustomerInfo>;
};

/** Filter input for customerInfo GraphQL type */
export type CustomerInfoFilterInput = {
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<CustomerInfoFilterInput>>>;
  /** Filter options for birthYear */
  birthYear: InputMaybe<ShortFilterInput>;
  /** Filter options for customerInfoAndData */
  customerInfoAndData: InputMaybe<CustomerDataFilterInput>;
  /** Filter options for deathYear */
  deathYear: InputMaybe<StringFilterInput>;
  /** Filter options for knownForTitles */
  knownForTitles: InputMaybe<StringFilterInput>;
  /** Filter options for nconst */
  nconst: InputMaybe<StringFilterInput>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<CustomerInfoFilterInput>>>;
  /** Filter options for primaryName */
  primaryName: InputMaybe<StringFilterInput>;
};

/** Order by input for customerInfo GraphQL type */
export type CustomerInfoOrderByInput = {
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<CustomerInfoOrderByInput>>>;
  /** Order by options for birthYear */
  birthYear: InputMaybe<OrderBy>;
  /** Order by options for customerInfoAndData */
  customerInfoAndData: InputMaybe<CustomerDataOrderByInput>;
  /** Order by options for deathYear */
  deathYear: InputMaybe<OrderBy>;
  /** Order by options for knownForTitles */
  knownForTitles: InputMaybe<OrderBy>;
  /** Order by options for nconst */
  nconst: InputMaybe<OrderBy>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<CustomerInfoOrderByInput>>>;
  /** Order by options for primaryName */
  primaryName: InputMaybe<OrderBy>;
};

/** Order by input for customer GraphQL type */
export type CustomerOrderByInput = {
  /** Order by options for CompanyName */
  CompanyName: InputMaybe<OrderBy>;
  /** Order by options for CustomerID */
  CustomerID: InputMaybe<OrderBy>;
  /** Order by options for EmailAddress */
  EmailAddress: InputMaybe<OrderBy>;
  /** Order by options for FirstName */
  FirstName: InputMaybe<OrderBy>;
  /** Order by options for LastName */
  LastName: InputMaybe<OrderBy>;
  /** Order by options for MiddleName */
  MiddleName: InputMaybe<OrderBy>;
  /** Order by options for ModifiedDate */
  ModifiedDate: InputMaybe<OrderBy>;
  /** Order by options for NameStyle */
  NameStyle: InputMaybe<OrderBy>;
  /** Order by options for PasswordHash */
  PasswordHash: InputMaybe<OrderBy>;
  /** Order by options for PasswordSalt */
  PasswordSalt: InputMaybe<OrderBy>;
  /** Order by options for Phone */
  Phone: InputMaybe<OrderBy>;
  /** Order by options for SalesPerson */
  SalesPerson: InputMaybe<OrderBy>;
  /** Order by options for Suffix */
  Suffix: InputMaybe<OrderBy>;
  /** Order by options for Title */
  Title: InputMaybe<OrderBy>;
  /** Order by options for addresses */
  addresses: InputMaybe<CustomerAddressOrderByInput>;
  /** Conditions to be treated as AND operations */
  and: InputMaybe<Array<InputMaybe<CustomerOrderByInput>>>;
  /** Conditions to be treated as OR operations */
  or: InputMaybe<Array<InputMaybe<CustomerOrderByInput>>>;
  /** Order by options for rowguid */
  rowguid: InputMaybe<OrderBy>;
};

export type RCustomer = {
  id: Maybe<Scalars['String']['output']>;
  type: Maybe<Scalars['String']['output']>;
  conversationId: Maybe<Scalars['String']['output']>;
  conversationStart: Maybe<Scalars['String']['output']>;
  conversationEnd: Maybe<Scalars['String']['output']>;
  conversationDuration: Maybe<Scalars['String']['output']>;
  direction: Maybe<Scalars['String']['output']>;
  users: Maybe<Scalars['String']['output']>;
  mediaType: Maybe<Scalars['String']['output']>;
  username: Maybe<Scalars['String']['output']>;
  dnis: Maybe<Scalars['String']['output']>;
  mediaUri: Maybe<Scalars['String']['output']>;
  ani: Maybe<Scalars['String']['output']>;
  transcript: Maybe<Scalars['String']['output']>;
  recordingMediaSource: Maybe<Scalars['String']['output']>;
  evaluation: Maybe<Scalars['String']['output']>;
  queues: Maybe<Scalars['String']['output']>;
  divisions: Maybe<Scalars['String']['output']>;
  customerRemote: Maybe<Scalars['String']['output']>;
  wrapups: Maybe<Scalars['String']['output']>;
  recording: Maybe<Scalars['String']['output']>;
  provider: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['String']['output']>;
};
