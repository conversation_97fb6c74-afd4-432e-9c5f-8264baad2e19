import { DUMMY_QM_FORM } from '@cdss-modules/design-system';

export const DUMMY_EVALUATION_FORMS = [
  {
    id: 'cse-1',
    label: 'Customer Service Evaluation Form',
    versions: [
      {
        id: 'cse-1-1',
        label: 'v2024-06-25',
      },
      {
        id: 'cse-1-2',
        label: 'v2024-04-15',
      },
    ],
    value: 'cse-1',
  },
  {
    id: 'se-1',
    label: 'Sales Evaluation Form',
    versions: [
      {
        id: 'se-1-1',
        label: 'v2024-03-01',
      },
    ],
    value: 'se-1',
  },
  {
    id: 'tse',
    label: 'Technical Support Evaluation Form',
    versions: [
      {
        id: 'tse-1-1',
        label: 'v2023-12-31',
      },
    ],
    value: 'tse',
  },
];

export const DUMMY_CS_EVALUATION_FORM = DUMMY_QM_FORM;

export const DUMMUY_QA_DATA = [
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000001',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-06-25',
    evaluator: 'Customer Service Evaluation Model (Auto QM)',
    isAuto: true,
    date: '2024-07-09T12:00:00Z',
    releasedAt: '2024-07-10T12:00:00Z',
    status: 'released',
  },
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000002',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-06-25',
    evaluator: 'Customer Service Evaluation Model (Auto QM)',
    isAuto: true,
    date: '2024-07-03T12:00:00Z',
    releasedAt: '2024-07-03T12:00:00Z',
    status: 'released',
  },
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000003',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-06-25',
    evaluator: 'Jane Doe',
    date: '2024-07-01T12:00:00Z',
    releasedAt: '2024-07-01T12:00:00Z',
    status: 'released',
  },
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000004',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-04-15',
    evaluator: 'Mary Poppins',
    date: '2024-07-01T12:00:00Z',
    status: 'evaluated',
  },
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000005',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-06-25',
    evaluator: 'Admin',
    score: null,
    assignedAt: '2024-07-01T12:00:00Z',
    status: 'assigned',
  },
];

export const DUMMY_DEFAULT_ANSWERS = {
  QADBMNLAM52MJ0GUEKVQ8I0ODO000001: {
    s_1: {
      duration: {
        start: 0,
        end: 30,
      },
      ss_1_1: {
        q_1_1_1: '1_1_1_y',
        q_1_1_2: '1_1_2_y',
      },
      ss_1_2: {
        q_1_2_1: '1_2_1_y',
      },
      ss_1_3: {
        q_1_3_1: '1_3_1_y',
      },
    },
    s_2: {
      duration: {
        start: 30,
        end: 120,
      },
      ss_2_1: {
        q_2_1_1: '2_1_1_y',
        q_2_1_2: '2_1_2_y',
      },
      ss_2_2: {
        q_2_2_1: '2_2_1_y',
        q_2_2_2: '2_2_2_y',
      },
      ss_2_3: {
        q_2_3_1: '2_3_1_y',
      },
    },
    s_3: {
      duration: {
        start: 120,
        end: 170,
      },
      ss_3_1: {
        q_3_1_1: '3_1_1_y',
      },
      ss_3_2: {
        q_3_2_1: '3_2_1_y',
      },
      ss_3_3: {
        q_3_3_1: '3_3_1_y',
        q_3_3_2: '3_3_2_y',
      },
    },
    s_4: {
      ss_4_1: {
        q4_1_1: 'Polite but bad closing.',
      },
    },
    s_5: {
      ss_5_1: {
        q5_1_1: '5_1_1_2',
      },
    },
    tags: {
      q_1_1_1: {
        ai: true,
        accuracy: 0.78,
        target: 's_1',
        type: 'positive',
        time: 5,
        label: 'Q.1.1.1',
        desc: 'Company name provided',
      },
      q_1_1_2: {
        ai: true,
        accuracy: 0.92,
        target: 's_1',
        type: 'positive',
        time: 10,
        label: 'Q.1.1.2',
        desc: 'Greeting provided',
      },
      q_1_2_1: {
        ai: true,
        accuracy: 0.84,
        target: 's_1',
        type: 'positive',
        time: 20,
        label: 'Q.1.2.1',
        desc: 'Name provided',
      },
      q_1_3_1: {
        ai: true,
        accuracy: 0.73,
        target: 's_1',
        type: 'positive',
        time: 25,
        label: 'Q.1.3.1',
        desc: 'Customer name requested',
      },
      q_2_1_1: {
        ai: true,
        accuracy: 0.85,
        target: 's_2',
        type: 'positive',
        time: 35,
        label: 'Q.2.1.1',
        desc: 'Asked for problem',
      },
      q_2_1_2: {
        ai: true,
        accuracy: 0.91,
        target: 's_2',
        type: 'positive',
        time: 40,
        label: 'Q.2.1.2',
        desc: 'Listened to customer',
      },
      q_2_2_1: {
        ai: true,
        accuracy: 0.88,
        target: 's_2',
        type: 'positive',
        time: 60,
        label: 'Q.2.2.1',
        desc: 'Clarified problem',
      },
      q_2_2_2: {
        ai: true,
        accuracy: 0.94,
        target: 's_2',
        type: 'positive',
        time: 70,
        label: 'Q.2.2.2',
        desc: 'Problem confirmed clarified',
      },
      q_2_3_1: {
        ai: true,
        accuracy: 0.82,
        target: 's_2',
        type: 'positive',
        time: 90,
        label: 'Q.2.3.1',
        desc: 'Solution provided',
      },
      q_3_1_1: {
        ai: true,
        accuracy: 0.89,
        target: 's_3',
        type: 'positive',
        time: 125,
        label: 'Q.3.1.1',
        desc: 'Problem resolution confirmed',
      },
      q_3_2_1: {
        ai: true,
        accuracy: 0.76,
        target: 's_3',
        type: 'positive',
        time: 135,
        label: 'Q.3.2.1',
        desc: 'Checked for additional problems',
      },
      q_3_3_1: {
        ai: true,
        accuracy: 0.83,
        target: 's_3',
        type: 'positive',
        time: 150,
        label: 'Q.3.3.1',
        desc: 'Feedback requested',
      },
      q_3_3_2: {
        ai: true,
        accuracy: 0.95,
        target: 's_3',
        type: 'positive',
        time: 160,
        label: 'Q.3.3.2',
        desc: 'Goodbye said',
      },
    },
  },
  QADBMNLAM52MJ0GUEKVQ8I0ODO000002: {
    s_1: {
      ss_1_1: {
        q_1_1_1: '1_1_1_y',
        q_1_1_2: '1_1_2_n',
      },
      ss_1_2: {
        q_1_2_1: '1_2_1_n',
      },
      ss_1_3: {
        q_1_3_1: '1_3_1_n',
      },
    },
    s_2: {
      ss_2_1: {
        q_2_1_1: '2_1_1_y',
        q_2_1_2: '2_1_2_y',
      },
      ss_2_2: {
        q_2_2_1: '2_2_1_n',
        q_2_2_2: '2_2_2_y',
      },
      ss_2_3: {
        q_2_3_1: '2_3_1_y',
      },
    },
    s_3: {
      ss_3_1: {
        q_3_1_1: '3_1_1_y',
      },
      ss_3_2: {
        q_3_2_1: '3_2_1_n',
      },
      ss_3_3: {
        q_3_3_1: '3_3_1_y',
        q_3_3_2: '3_3_2_y',
      },
    },
    s_4: {
      ss_4_1: {
        q4_1_1: 'Bad closing.',
      },
    },
    s_5: {
      ss_5_1: {
        q5_1_1: '5_1_1_2',
      },
    },
    tags: {
      q_1_1_1: {
        ai: true,
        accuracy: 0.72,
        target: 's_1',
        type: 'positive',
        time: 5,
        label: 'Q.1.1.1',
        desc: 'Company name provided',
      },
      q_1_1_2: {
        ai: true,
        accuracy: 0.45,
        target: 's_1',
        type: 'negative',
        time: 10,
        label: 'Q.1.1.2',
        desc: 'Greeting not provided',
      },
      q_1_2_1: {
        ai: true,
        accuracy: 0.3,
        target: 's_1',
        type: 'negative',
        time: 15,
        label: 'Q.1.2.1',
        desc: "Agent's name not provided",
      },
      q_1_3_1: {
        ai: true,
        accuracy: 0.2,
        target: 's_1',
        type: 'negative',
        time: 20,
        label: 'Q.1.3.1',
        desc: "Customer's name not requested",
      },
      q_2_1_1: {
        ai: true,
        accuracy: 0.85,
        target: 's_2',
        type: 'positive',
        time: 30,
        label: 'Q.2.1.1',
        desc: 'Asked for problem',
      },
      q_2_1_2: {
        ai: true,
        accuracy: 0.9,
        target: 's_2',
        type: 'positive',
        time: 35,
        label: 'Q.2.1.2',
        desc: 'Listened to customer',
      },
      q_2_2_1: {
        ai: true,
        accuracy: 0.4,
        target: 's_2',
        type: 'negative',
        time: 50,
        label: 'Q.2.2.1',
        desc: 'Failed to clarify problem',
      },
      q_2_2_2: {
        ai: true,
        accuracy: 0.88,
        target: 's_2',
        type: 'positive',
        time: 55,
        label: 'Q.2.2.2',
        desc: 'Problem clarification confirmed',
      },
      q_2_3_1: {
        ai: true,
        accuracy: 0.8,
        target: 's_2',
        type: 'positive',
        time: 75,
        label: 'Q.2.3.1',
        desc: 'Solution provided',
      },
      q_3_1_1: {
        ai: true,
        accuracy: 0.82,
        target: 's_3',
        type: 'positive',
        time: 85,
        label: 'Q.3.1.1',
        desc: 'Problem resolution confirmed',
      },
      q_3_2_1: {
        ai: true,
        accuracy: 0.25,
        target: 's_3',
        type: 'negative',
        time: 95,
        label: 'Q.3.2.1',
        desc: 'Did not ask for additional problems',
      },
      q_3_3_1: {
        ai: true,
        accuracy: 0.78,
        target: 's_3',
        type: 'positive',
        time: 105,
        label: 'Q.3.3.1',
        desc: 'Feedback requested',
      },
      q_3_3_2: {
        ai: true,
        accuracy: 0.92,
        target: 's_3',
        type: 'positive',
        time: 110,
        label: 'Q.3.3.2',
        desc: 'Goodbye said',
      },
    },
  },
  QADBMNLAM52MJ0GUEKVQ8I0ODO000003: {
    s_1: {
      ss_1_1: {
        q_1_1_1: '1_1_1_y',
        q_1_1_2: '1_1_2_n',
      },
      ss_1_2: {
        q_1_2_1: '1_2_1_n',
      },
      ss_1_3: {
        q_1_3_1: '1_3_1_n',
      },
    },
    s_2: {
      ss_2_1: {
        q_2_1_1: '2_1_1_y',
        q_2_1_2: '2_1_2_y',
      },
      ss_2_2: {
        q_2_2_1: '2_2_1_n',
        q_2_2_2: '2_2_2_y',
      },
      ss_2_3: {
        q_2_3_1: '2_3_1_y',
      },
    },
    s_3: {
      ss_3_1: {
        q_3_1_1: '3_1_1_y',
      },
      ss_3_2: {
        q_3_2_1: '3_2_1_n',
      },
      ss_3_3: {
        q_3_3_1: '3_3_1_y',
        q_3_3_2: '3_3_2_y',
      },
    },
    s_4: {
      ss_4_1: {
        q4_1_1: 'Impolite closing.',
      },
    },
    s_5: {
      ss_5_1: {
        q5_1_1: '5_1_1_2',
      },
    },
    tags: {
      q_1_1_1: {
        target: 's_1',
        time: 10,
        type: 'positive',
        label: 'Q.1.1.1',
        desc: 'Name Provided',
      },
      q_1_1_2: {
        target: 's_1',
        time: 20,
        type: 'negative',
        label: 'Q.1.1.2',
        desc: 'No greetings',
      },
      q_1_2_1: {
        target: 's_1',
        time: 100,
        type: 'negative',
        label: 'Q.1.2.1',
        desc: 'Agent name not provided',
      },
      q_2_2_1: {
        target: 's_2',
        time: 130,
        type: 'negative',
        label: 'Q.2.2.1',
        desc: 'Not clarifying the problem',
      },
      q_3_2_1: {
        target: 's_3',
        time: 160,
        type: 'negative',
        label: 'Q.3.2.1',
        desc: 'Not asking for other problems',
      },
    },
  },
  QADBMNLAM52MJ0GUEKVQ8I0ODO000004: {
    s_1: {
      ss_1_1: {
        q_1_1_1: '1_1_1_y',
        q_1_1_2: '1_1_2_n',
      },
      ss_1_2: {
        q_1_2_1: '1_2_1_n',
      },
    },
    s_2: {
      ss_2_1: {
        q_2_1_1: '2_1_1_y',
        q_2_1_2: '2_1_2_y',
      },
    },
    s_3: {
      ss_3_1: {
        q_3_1_1: '3_1_1_y',
      },
      ss_3_2: {
        q_3_2_1: '3_2_1_y',
      },
      ss_3_3: {
        q_3_3_1: '3_3_1_y',
        q_3_3_2: '3_3_2_y',
      },
    },
  },
};
