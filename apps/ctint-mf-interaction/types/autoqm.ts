export interface QmFormList {
  data: QmFormListItem[];
}

export interface QmFormListItem {
  formId: string;
  formName: string;
  category: string;
  type: string;
  language: string;
  formVersion: string;
}

export interface QmEvaluationList {
  data: QmEvaluationListItem[];
}

export interface QmEvaluationListItem {
  evaluationId: string;
  formId: string;
  formName: string;
  formVersion: string;
  evaluatorName: string;
  score: number;
  /**
   * 评估状态
   * - init: 初始化, AutoQM 开始自动评估
   * - inprogress: 进行中, AutoQM 正在自动评估
   * - completed: 完成, AutoQM 完成自动评估，用户可以查看评估结果和编辑
   * - failed: 失败, AutoQM 自动评估失败，用户不可以查看评估结果
   * - published: 发布, 评估发布，用户可以查看评估结果但是不能编辑
   */
  status: 'init' | 'inprogress' | 'completed' | 'failed' | 'published';
  processBy: string; // 处理人
  finalResult: string; // 'pass' | 'fail'
  releasedTime: string; // AutoQM 转换完的时间，不是评估publish时间
  manualResult?: string; // 人工结果
  manualDetailList?: ManualDetailList[];
}

export interface ManualDetailList {
  manualDetailId: string;
  type: string;
  referenceId: string;
  tenant: string;
  platform: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
  manualResult: string;
  comment: string;
}

// API 返回数据
export interface QmResultItem {
  metaDataResult: MetaDataResult; // 元数据结果
  finalRate: number; // 最终得分
  finalResult: string; // 最终结果
  manualResult: string; // 人工结果
  comment: string; // 备注
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  createBy: string; // 创建人
  updateBy: string;
  platform: string;
  tenant: string;
  evaluationId: string; // 评估id
  extractionResult: string;
  similarityResult: SimilarityResult; // 相似度结果
  standardScriptId: string;
  transcriptMasterId: string;
  nplResultId: string; // 结果id
  timerStart?: string;
  timerEnd?: string;
  systemNote?: string;
  displayContent?: string;
  standardScript: StandardScript;
  manualDetailList?: ManualDetailItem[];
  importantWordResult: ImportantWordResult; // 重要词结果
  othersResult?: QmResultItem[];
}

// API 返回数据
export interface StandardScript {
  standardScriptId: string;
  formId: string;
  stepId: string; // 主步骤id
  scenarioId: string; // 子步骤id
  content: string; // 子步骤内容
  helpContent: string; // ？
  participant: string;
  doSimilarity: boolean;
  doExtraction: boolean;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
  platform: string;
  tenant: string;
}

export interface ManualDetailItem {
  manualDetailId: string;
  type: string;
  referenceId: string;
  tenant: string;
  platform: string;
  createTime: string; // 创建时间
  updateTime: string;
  createBy: string; // 创建人
  updateBy: string;
  manualResult: string; // 人工结果 Passed | Failed
  comment: string; // 备注
}

export interface StandardScriptItem {
  standardScriptId: string;
  formId: string;
  stepId: string;
  scenarioId: string;
  content: string;
  helpContent: string;
  participant: string;
  doSimilarity: boolean;
  doExtraction: boolean;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
  platform: string;
  tenant: string;
}

export interface MetaDataResult {
  entityResults?: EntityResult[];
  matchMinCorrect?: boolean;
  allMatch?: boolean;
}

export interface EntityResult {
  entityType?: string;
  entityValue?: string;
  speaker?: string;
  entityValueMatch?: boolean;
}

export interface SimilarityResult {
  similarityResultDetailList?: SimilarityResultDetailList[];
  allMatch?: boolean;
}

export interface SimilarityResultDetailList {
  similarityScore?: number;
  similarityResult?: boolean;
  startTime?: string;
  endTime?: string;
  speaker?: string;
  text_segment_with_higheset_similarity?: TextSegmentWithHighesetSimilarity;
}

export interface TextSegmentWithHighesetSimilarity {
  transcript?: string;
  standard_script?: string;
}

export interface ImportantWordResult {
  importantWordDetailList?: ImportantWordDetailList[];
  allMatch?: boolean;
}

export interface ImportantWordDetailList {
  entityType?: string;
  entityValue?: string;
  speaker?: string;
  existed?: boolean;
  transcripts?: string[];
}
