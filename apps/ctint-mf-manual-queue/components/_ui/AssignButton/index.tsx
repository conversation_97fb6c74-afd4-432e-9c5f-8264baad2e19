import { Row } from '@tanstack/react-table';
import {
  AssignReq,
  ManualQueueData, ManualQueuePermission, microfrontends,
  User,
} from '../../../types/microfrontendsConfig';
import React, { useRef, useState } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  THEME_FONT_COLORS, useRole,
  useRouteHandler,
  useToast,
} from '@cdss-modules/design-system';
import { assignTo } from '../../../lib/api';
import { createPortal } from 'react-dom';
import UserSelector from '../UserSelector';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';

export interface AssignButtonProps {
  row: Row<ManualQueueData>;
  onAssignmentSuccess: () => void;
  type: string;
}

const AssignButton: React.FC<AssignButtonProps> = ({
  row,
  onAssignmentSuccess,
  type,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showUserSelector, setShowUserSelector] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();
  const { toast } = useToast();
  const isEnabled =
    row.original.state === 'WAITING' || row.original.state === 'ALERTING';
  const handleAssignToMe = (e: React.MouseEvent) => {
    //防止点击按钮时触发父元素的点击事件处理器
    e.stopPropagation();

    const assignReq: AssignReq = {
      isAssignToMe: true,
      conversationId: row.original.conversationId,
    };

    assignTo(basePath, assignReq)
      .then((result) => {
        toast({
          title: 'Success',
          description: 'Assign successfully sent',
          variant: 'success',
        });
        //刷新页面
        setTimeout(() => {
          onAssignmentSuccess();
        }, 1000);
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.error || 'Assign Error';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(() => setIsOpen(false));
  };

  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const manualQueuePermission: ManualQueuePermission =
    microfrontendsConfig['ctint-mf-manual-queue']['permission'];

  console.log('permissions', permissions);
  console.log('manualQueuePermission', manualQueuePermission);

  let assignToPermission: string | undefined;
  if (type === 'voicemail') {
    assignToPermission = manualQueuePermission?.['voicemail-assign-to'];
  } else if (type === 'callback') {
    assignToPermission = manualQueuePermission?.['callback-assign-to'];
  }
  const canAssignTo =
    !assignToPermission || permissions.includes(assignToPermission);

  const handleUserSelected = (user: User) => {
    const assignReq: AssignReq = {
      agentId: user.id,
      conversationId: row.original.conversationId,
    };

    assignTo(basePath, assignReq)
      .then((result) => {
        setShowUserSelector(false);
        toast({
          title: 'Success',
          description: 'Assign successfully sent',
          variant: 'success',
        });
        //刷新页面
        setTimeout(() => {
          onAssignmentSuccess();
        }, 1000);
      })
      .catch((error) => {
        console.log(error);
        const errorMessage = error.response?.data?.error || 'Operation Failed';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(() => setIsOpen(false));
  };

  const handleAssignTo = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(false);
    setShowUserSelector(true);
  };

  const renderDropdown = () => {
    if (!isOpen || !buttonRef.current) return null;

    const rect = buttonRef.current.getBoundingClientRect();
    const dropdownStyle = {
      position: 'fixed',
      top: `${rect.bottom + window.scrollY + 5}px`,
      left: `${rect.right + window.scrollX - 120}px`,
      minWidth: '120px',
      zIndex: 9999,
    } as const;

    return createPortal(
      <div
        role="menu"
        className="bg-white rounded shadow-lg border border-gray-100 py-1"
        style={dropdownStyle}
      >
        <button
          role="menuitem"
          onClick={handleAssignToMe}
          className="w-full px-4 py-2 text-sm hover:bg-primary-100 text-left block"
        >
          <span className="text-common-black">
            {t('assign-to-me', 'Assign to me')}
          </span>
        </button>
        {canAssignTo && (
          <button
            role="menuitem"
            onClick={handleAssignTo}
            className={`w-full px-4 py-2 text-sm hover:bg-primary-100 text-left block ${canAssignTo ? 'border-t border-gray-100' : ''}`}
          >
            <span className="text-common-black">
              {t('assign-to', 'Assign to...')}
            </span>
          </button>
        )}
      </div>,
      document.body
    );
  };

  return (
    <div
      ref={buttonRef}
      className="relative inline-flex"
    >
      <button
        onClick={(e: React.MouseEvent) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
          setShowUserSelector(false);
        }}
        disabled={!isEnabled}
        className={`flex items-center hover:bg-transparent px-5 min-w-[40px] ${!isEnabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <svg
          width="4"
          height="15"
          viewBox="0 0 4 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            className={`mr-1 ${!isEnabled ? 'opacity-50' : ''}`}
            d="M2.00139 14.543C1.50908 14.543 1.03694 14.3463 0.688825 13.9962C0.34071 13.6462 0.145142 13.1714 0.145142 12.6763C0.145142 12.1812 0.34071 11.7064 0.688825 11.3564C1.03694 11.0063 1.50908 10.8096 2.00139 10.8096C2.4937 10.8096 2.96585 11.0063 3.31396 11.3564C3.66207 11.7064 3.85764 12.1812 3.85764 12.6763C3.85764 13.1714 3.66207 13.6462 3.31396 13.9962C2.96585 14.3463 2.4937 14.543 2.00139 14.543ZM2.00139 9.20964C1.50908 9.20964 1.03694 9.01297 0.688825 8.6629C0.34071 8.31283 0.145142 7.83804 0.145142 7.34297C0.145142 6.8479 0.34071 6.3731 0.688825 6.02304C1.03694 5.67297 1.50908 5.4763 2.00139 5.4763C2.4937 5.4763 2.96585 5.67297 3.31396 6.02304C3.66207 6.3731 3.85764 6.8479 3.85764 7.34297C3.85764 7.83804 3.66207 8.31283 3.31396 8.6629C2.96585 9.01297 2.4937 9.20964 2.00139 9.20964ZM0.145142 2.00963C0.145142 1.51456 0.34071 1.03977 0.688825 0.689701C1.03694 0.339634 1.50908 0.142968 2.00139 0.142968C2.4937 0.142968 2.96585 0.339634 3.31396 0.689701C3.66207 1.03977 3.85764 1.51456 3.85764 2.00963C3.85764 2.50471 3.66207 2.9795 3.31396 3.32957C2.96585 3.67964 2.4937 3.8763 2.00139 3.8763C1.50908 3.8763 1.03694 3.67964 0.688825 3.32957C0.34071 2.9795 0.145142 2.50471 0.145142 2.00963Z"
            style={{
              fill: isOpen
                ? THEME_FONT_COLORS.primary[500] // '#ffac4a'
                : THEME_FONT_COLORS.secondary[500], // '#30302f'
            }}
          />
        </svg>
      </button>
      {renderDropdown()}
      {showUserSelector && (
        <UserSelector
          isOpen={showUserSelector}
          onClose={() => setShowUserSelector(false)}
          anchorRef={buttonRef}
          onSelectUser={handleUserSelected}
        />
      )}
    </div>
  );
};

export default AssignButton;
