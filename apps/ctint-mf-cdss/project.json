{"name": "ctint-mf-cdss", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-cdss", "projectType": "application", "implicitDependencies": ["ctint-mf-*"], "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-cdss", "outputPath": "dist/apps/ctint-mf-cdss"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-cdss:build", "dev": true, "port": 4400, "host": "0.0.0.0", "experimentalHttps": false}, "configurations": {"development": {"buildTarget": "ctint-mf-cdss:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-cdss:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-cdss:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-cdss"], "options": {"jestConfig": "apps/ctint-mf-cdss/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-cdss/**/*.{ts,tsx,js,jsx}"]}}}}