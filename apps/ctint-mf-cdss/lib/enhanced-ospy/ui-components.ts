/**
 * 增强版 O-Spy UI 组件
 * 提供浮窗界面和交互功能
 */

import { ExportManager } from './export-manager';
import { OSpySessionDB, SessionData } from './session-db';

// 定义 EnhancedOSpy 接口
interface IEnhancedOSpy {
  getSessionDB(): OSpySessionDB;
  getCurrentSession(): SessionData | null;
  getIsRecording(): boolean;
  startRecording(): Promise<void>;
  stopRecording(): Promise<void>;
}

export class EnhancedOSpyUI {
  private enhancedOSpy: IEnhancedOSpy;
  private exportManager: ExportManager;
  private floatingPanel: HTMLElement | null = null;
  private isMinimized = false;
  private isDragging = false;
  private dragOffset = { x: 0, y: 0 };
  private dragBounds = { maxX: 0, maxY: 0 };
  private rafId: number | null = null;
  private currentPosition = { x: 0, y: 0 };
  private pendingPosition = { x: 0, y: 0 };

  constructor(enhancedOSpy: IEnhancedOSpy) {
    this.enhancedOSpy = enhancedOSpy;
    this.exportManager = new ExportManager(enhancedOSpy.getSessionDB());
  }

  /**
   * 创建浮动面板
   */
  createFloatingPanel(): HTMLElement {
    // 如果已存在，先移除
    if (this.floatingPanel) {
      this.floatingPanel.remove();
    }

    const panel = document.createElement('div');
    panel.className = 'enhanced-ospy-panel';
    panel.innerHTML = this.getPanelHTML();

    this.setupPanelStyles();
    this.setupEventListeners(panel);
    document.body.appendChild(panel);
    this.floatingPanel = panel;

    // 初始化位置状态
    this.initializePosition();

    console.log('增强版 O-Spy 浮窗创建完成');
    return panel;
  }

  /**
   * 获取面板HTML
   */
  private getPanelHTML(): string {
    return `
      <div class="ospy-header">
        <span class="ospy-title">🔍 O-Spy Enhanced</span>
        <div class="ospy-controls">
          <button class="ospy-minimize" title="最小化">−</button>
          <button class="ospy-close" title="关闭">×</button>
        </div>
      </div>
      <div class="ospy-content">
        <div class="ospy-recording-section">
          <button class="ospy-record-btn" data-action="toggle-recording">
            <span class="record-icon">●</span>
            <span class="record-text">开始记录</span>
          </button>
          <div class="ospy-status">
            <span class="status-text">就绪</span>
            <span class="session-timer">00:00:00</span>
          </div>
        </div>

        <div class="ospy-export-section">
          <button class="ospy-export-current" disabled>导出当前会话</button>
          <button class="ospy-view-history">查看历史记录</button>
        </div>

        <div class="ospy-history-panel" style="display: none;">
          <div class="history-header">
            <div class="history-title-row">
              <h3>历史会话</h3>
              <button class="history-close">×</button>
            </div>
            <div class="history-search-row">
              <input type="text" placeholder="搜索会话..." class="history-search">
              <select class="history-filter">
                <option value="">所有状态</option>
                <option value="completed">已完成</option>
                <option value="interrupted">已中断</option>
                <option value="recording">记录中</option>
              </select>
            </div>
            <div class="history-actions-row">
              <button class="history-export-all">导出全部</button>
              <button class="history-clear-all">清空历史</button>
            </div>
          </div>
          <div class="history-stats"></div>
          <div class="history-list-container">
            <div class="history-list"></div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置面板样式
   */
  private setupPanelStyles(): void {
    if (document.getElementById('enhanced-ospy-styles')) {
      return;
    }

    const styles = `
      .enhanced-ospy-panel {
        position: fixed;
        top: 0;
        left: 0;
        width: 320px;
        height: auto;
        max-height: 80vh;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        border: 1px solid #e1e5e9;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        will-change: transform;
        transform: translate3d(0, 0, 0);
      }

      .enhanced-ospy-panel.dragging {
        transition: none;
        box-shadow: 0 12px 40px rgba(0,0,0,0.2);
      }

      .enhanced-ospy-panel.minimized {
        height: 50px;
        overflow: hidden;
      }

      .ospy-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px 12px 0 0;
        cursor: move;
        user-select: none;
        touch-action: none;
      }

      .enhanced-ospy-panel.dragging .ospy-header {
        cursor: grabbing;
      }

      .ospy-title {
        font-weight: 600;
        font-size: 14px;
      }

      .ospy-controls {
        display: flex;
        gap: 8px;
      }

      .ospy-controls button {
        background: rgba(255,255,255,0.2);
        border: none;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        transition: background 0.2s;
      }

      .ospy-controls button:hover {
        background: rgba(255,255,255,0.3);
      }

      .ospy-content {
        padding: 16px;
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
      }

      .ospy-recording-section {
        margin-bottom: 16px;
      }

      .ospy-record-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 20px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        width: 100%;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s;
      }

      .ospy-record-btn:hover {
        background: #218838;
        transform: translateY(-1px);
      }

      .ospy-record-btn.recording {
        background: #dc3545;
        animation: pulse 2s infinite;
      }

      .ospy-record-btn.recording:hover {
        background: #c82333;
      }

      @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
        100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
      }

      .record-icon {
        font-size: 16px;
      }

      .ospy-status {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        font-size: 12px;
      }

      .status-text {
        color: #6c757d;
      }

      .session-timer {
        font-family: 'Monaco', 'Menlo', monospace;
        font-weight: 600;
        color: #495057;
      }

      .ospy-export-section {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .ospy-export-section button {
        padding: 10px 16px;
        border: 1px solid #dee2e6;
        background: white;
        border-radius: 6px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.2s;
      }

      .ospy-export-section button:hover:not(:disabled) {
        background: #f8f9fa;
        border-color: #adb5bd;
      }

      .ospy-export-section button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .ospy-export-current {
        color: #007bff;
        border-color: #007bff;
      }

      .ospy-export-current:hover:not(:disabled) {
        background: #007bff;
        color: white;
      }

      .ospy-view-history {
        color: #6c757d;
      }

      .ospy-history-panel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: white;
        border-radius: 12px;
        z-index: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .history-header {
        padding: 16px;
        border-bottom: 1px solid #e1e5e9;
        flex-shrink: 0;
      }

      .history-title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .history-header h3 {
        margin: 0;
        font-size: 16px;
        color: #495057;
      }

      .history-search-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
      }

      .history-actions-row {
        display: flex;
        gap: 8px;
      }

      .history-search {
        flex: 1;
        padding: 6px 12px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        font-size: 12px;
        min-width: 0;
      }

      .history-filter {
        padding: 6px 12px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        font-size: 12px;
        background: white;
        cursor: pointer;
        min-width: 100px;
      }

      .history-export-all,
      .history-clear-all {
        padding: 6px 12px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        background: white;
        transition: all 0.2s;
        flex: 1;
        white-space: nowrap;
      }

      .history-export-all {
        color: #007bff;
        border-color: #007bff;
      }

      .history-export-all:hover {
        background: #007bff;
        color: white;
      }

      .history-clear-all {
        color: #dc3545;
        border-color: #dc3545;
      }

      .history-clear-all:hover {
        background: #dc3545;
        color: white;
      }

      .history-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        color: #6c757d;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .history-stats {
        padding: 12px 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #e1e5e9;
        font-size: 12px;
        color: #6c757d;
        display: flex;
        justify-content: space-between;
        flex-shrink: 0;
      }

      .history-stats .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .history-stats .stat-value {
        font-weight: 600;
        color: #495057;
      }

      .history-list-container {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        min-height: 0;
        max-height: 250px;
      }

      .history-list {
        flex: 1;
        overflow-y: scroll !important;
        overflow-x: hidden;
        padding: 8px;
        min-height: 0;
        height: 100%;
        max-height: 250px;
      }

      /* 强制显示滚动条 */
      .history-list::-webkit-scrollbar {
        width: 8px !important;
        display: block !important;
      }

      .history-list::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 4px !important;
      }

      .history-list::-webkit-scrollbar-thumb {
        background: #c1c1c1 !important;
        border-radius: 4px !important;
        min-height: 20px !important;
      }

      .history-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8 !important;
      }

      /* 为Firefox添加滚动条样式 */
      .history-list {
        scrollbar-width: thin !important;
        scrollbar-color: #c1c1c1 #f1f1f1 !important;
      }

      .history-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        border: 1px solid #e1e5e9;
        border-radius: 6px;
        margin-bottom: 8px;
        background: white;
        transition: all 0.2s;
      }

      .history-item:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
      }

      .history-item-info {
        flex: 1;
      }

      .history-item-title {
        font-size: 13px;
        font-weight: 500;
        color: #495057;
        margin-bottom: 4px;
      }

      .history-item-meta {
        font-size: 11px;
        color: #6c757d;
      }

      .history-item-actions {
        display: flex;
        gap: 4px;
      }

      .history-item-actions button {
        padding: 4px 8px;
        border: none;
        border-radius: 4px;
        font-size: 11px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .export-btn {
        background: #007bff;
        color: white;
      }

      .export-btn:hover {
        background: #0056b3;
      }

      .delete-btn {
        background: #dc3545;
        color: white;
      }

      .delete-btn:hover {
        background: #c82333;
      }
    `;

    const styleSheet = document.createElement('style');
    styleSheet.id = 'enhanced-ospy-styles';
    styleSheet.textContent = styles;
    document.head.appendChild(styleSheet);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(panel: HTMLElement): void {
    // 记录按钮
    const recordBtn = panel.querySelector(
      '.ospy-record-btn'
    ) as HTMLButtonElement;
    recordBtn?.addEventListener('click', () => {
      if (this.enhancedOSpy.getIsRecording()) {
        this.enhancedOSpy.stopRecording();
      } else {
        this.enhancedOSpy.startRecording();
      }
    });

    // 导出当前会话
    const exportCurrentBtn = panel.querySelector(
      '.ospy-export-current'
    ) as HTMLButtonElement;
    exportCurrentBtn?.addEventListener('click', () => {
      this.exportCurrentSession();
    });

    // 查看历史记录
    const viewHistoryBtn = panel.querySelector(
      '.ospy-view-history'
    ) as HTMLButtonElement;
    viewHistoryBtn?.addEventListener('click', () => {
      this.showHistoryPanel();
    });

    // 关闭历史面板
    const historyCloseBtn = panel.querySelector(
      '.history-close'
    ) as HTMLButtonElement;
    historyCloseBtn?.addEventListener('click', () => {
      this.hideHistoryPanel();
    });

    // 最小化按钮
    const minimizeBtn = panel.querySelector(
      '.ospy-minimize'
    ) as HTMLButtonElement;
    minimizeBtn?.addEventListener('click', () => {
      this.toggleMinimize();
    });

    // 关闭按钮
    const closeBtn = panel.querySelector('.ospy-close') as HTMLButtonElement;
    closeBtn?.addEventListener('click', () => {
      this.closePanel();
    });

    // 拖拽功能
    this.setupDragFunctionality(panel);

    // 历史搜索
    const searchInput = panel.querySelector(
      '.history-search'
    ) as HTMLInputElement;
    searchInput?.addEventListener('input', (e) => {
      this.filterHistory((e.target as HTMLInputElement).value);
    });

    // 状态过滤
    const filterSelect = panel.querySelector(
      '.history-filter'
    ) as HTMLSelectElement;
    filterSelect?.addEventListener('change', (e) => {
      this.filterHistoryByStatus((e.target as HTMLSelectElement).value);
    });

    // 导出全部
    const exportAllBtn = panel.querySelector(
      '.history-export-all'
    ) as HTMLButtonElement;
    exportAllBtn?.addEventListener('click', () => {
      this.exportAllSessions();
    });

    // 清空历史
    const clearAllBtn = panel.querySelector(
      '.history-clear-all'
    ) as HTMLButtonElement;
    clearAllBtn?.addEventListener('click', () => {
      this.clearAllHistory();
    });
  }

  /**
   * 设置拖拽功能
   */
  private setupDragFunctionality(panel: HTMLElement): void {
    const header = panel.querySelector('.ospy-header') as HTMLElement;

    header.addEventListener('mousedown', (e) => {
      this.startDrag(e, panel);
    }, { passive: false });

    // 支持触摸设备
    header.addEventListener('touchstart', (e) => {
      if (e.touches.length === 1) {
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousedown', {
          clientX: touch.clientX,
          clientY: touch.clientY,
        });
        this.startDrag(mouseEvent, panel);
      }
    }, { passive: false });
  }

  private startDrag(e: MouseEvent, panel: HTMLElement): void {
    this.isDragging = true;

    // 计算鼠标相对于面板左上角的偏移
    // 使用我们维护的currentPosition而不是getBoundingClientRect
    this.dragOffset = {
      x: e.clientX - this.currentPosition.x,
      y: e.clientY - this.currentPosition.y,
    };

    // 缓存边界计算
    this.dragBounds = {
      maxX: window.innerWidth - panel.offsetWidth,
      maxY: window.innerHeight - panel.offsetHeight,
    };

    // 添加拖动状态类
    panel.classList.add('dragging');

    // 绑定事件
    document.addEventListener('mousemove', this.handleDrag, { passive: true });
    document.addEventListener('mouseup', this.handleDragEnd, { passive: true });
    document.addEventListener('touchmove', this.handleTouchMove, { passive: false });
    document.addEventListener('touchend', this.handleDragEnd, { passive: true });

    e.preventDefault();
  }

  private handleDrag = (e: MouseEvent) => {
    if (!this.isDragging || !this.floatingPanel) return;

    // 计算新位置：鼠标位置 - 偏移量
    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;

    // 限制在视窗内
    this.pendingPosition = {
      x: Math.max(0, Math.min(x, this.dragBounds.maxX)),
      y: Math.max(0, Math.min(y, this.dragBounds.maxY)),
    };

    // 更新当前位置状态
    this.currentPosition = { ...this.pendingPosition };

    // 使用 RAF 优化性能
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
    }

    this.rafId = requestAnimationFrame(() => {
      this.updatePanelPosition();
    });
  };

  private handleTouchMove = (e: TouchEvent) => {
    if (e.touches.length === 1) {
      const touch = e.touches[0];
      const mouseEvent = new MouseEvent('mousemove', {
        clientX: touch.clientX,
        clientY: touch.clientY,
      });
      this.handleDrag(mouseEvent);
      e.preventDefault();
    }
  };

  private updatePanelPosition(): void {
    if (!this.floatingPanel) return;

    // 使用 transform 进行定位以获得更好的性能
    const { x, y } = this.pendingPosition;
    this.floatingPanel.style.transform = `translate3d(${x}px, ${y}px, 0)`;
  }

  private handleDragEnd = () => {
    if (!this.isDragging || !this.floatingPanel) return;

    this.isDragging = false;

    // 清理RAF
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }

    // 移除拖动状态类
    this.floatingPanel.classList.remove('dragging');

    // 保持使用transform定位，不转换为left/top
    // 这样可以避免下次拖动时的位置计算问题

    // 移除事件监听器
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.handleDragEnd);
    document.removeEventListener('touchmove', this.handleTouchMove);
    document.removeEventListener('touchend', this.handleDragEnd);
  };

  /**
   * 初始化面板位置
   */
  private initializePosition(): void {
    if (!this.floatingPanel) return;

    // 等待下一帧，确保面板已经渲染
    requestAnimationFrame(() => {
      if (!this.floatingPanel) return;

      // 计算右下角位置
      this.currentPosition = {
        x: window.innerWidth - this.floatingPanel.offsetWidth - 20,
        y: window.innerHeight - this.floatingPanel.offsetHeight - 20,
      };

      // 设置初始位置
      this.floatingPanel.style.transform = `translate3d(${this.currentPosition.x}px, ${this.currentPosition.y}px, 0)`;
    });
  }

  /**
   * 更新记录状态
   */
  updateRecordingState(isRecording: boolean): void {
    if (!this.floatingPanel) return;

    const recordBtn = this.floatingPanel.querySelector(
      '.ospy-record-btn'
    ) as HTMLButtonElement;
    const recordText = this.floatingPanel.querySelector(
      '.record-text'
    ) as HTMLSpanElement;
    const statusText = this.floatingPanel.querySelector(
      '.status-text'
    ) as HTMLSpanElement;
    const exportBtn = this.floatingPanel.querySelector(
      '.ospy-export-current'
    ) as HTMLButtonElement;

    if (isRecording) {
      recordBtn.classList.add('recording');
      recordText.textContent = '停止记录';
      statusText.textContent = '记录中';
      exportBtn.disabled = false;
    } else {
      recordBtn.classList.remove('recording');
      recordText.textContent = '开始记录';
      statusText.textContent = '就绪';
      exportBtn.disabled = true;
    }
  }

  /**
   * 更新计时器
   */
  updateTimer(elapsed: number): void {
    if (!this.floatingPanel) return;

    const timer = this.floatingPanel.querySelector(
      '.session-timer'
    ) as HTMLSpanElement;
    const hours = Math.floor(elapsed / 3600000);
    const minutes = Math.floor((elapsed % 3600000) / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);

    timer.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * 导出当前会话
   */
  private async exportCurrentSession(): Promise<void> {
    try {
      await this.exportManager.exportCurrentSession(this.enhancedOSpy);
    } catch (error) {
      console.error('导出当前会话失败:', error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      alert('导出失败: ' + errorMessage);
    }
  }

  /**
   * 显示历史面板
   */
  private async showHistoryPanel(): Promise<void> {
    if (!this.floatingPanel) return;

    // 如果当前是最小化状态，先取消最小化
    if (this.isMinimized) {
      this.isMinimized = false;
      this.floatingPanel.classList.remove('minimized');
    }

    // 设置面板高度为440px
    this.floatingPanel.style.height = '440px';

    const historyPanel = this.floatingPanel.querySelector(
      '.ospy-history-panel'
    ) as HTMLElement;
    historyPanel.style.display = 'block';

    await this.loadHistoryList();
    await this.updateHistoryStats();
  }

  /**
   * 隐藏历史面板
   */
  private hideHistoryPanel(): void {
    if (!this.floatingPanel) return;

    // 隐藏历史面板
    const historyPanel = this.floatingPanel.querySelector(
      '.ospy-history-panel'
    ) as HTMLElement;
    historyPanel.style.display = 'none';

    // 恢复面板高度，但要考虑最小化状态
    if (this.isMinimized) {
      // 如果当前是最小化状态，保持最小化高度
      this.floatingPanel.style.height = '50px';
    } else {
      // 否则恢复为auto
      this.floatingPanel.style.height = 'auto';
    }
  }

  /**
   * 加载历史列表
   */
  private async loadHistoryList(): Promise<void> {
    if (!this.floatingPanel) return;

    const historyList = this.floatingPanel.querySelector(
      '.history-list'
    ) as HTMLElement;

    try {
      const sessions = await this.enhancedOSpy.getSessionDB().getAllSessions();

      if (sessions.length === 0) {
        historyList.innerHTML =
          '<div style="text-align: center; padding: 20px; color: #6c757d;">暂无历史记录</div>';
        return;
      }

      historyList.innerHTML = sessions
        .map((session) => this.createHistoryItemHTML(session))
        .join('');

      // 绑定历史项目的事件
      this.bindHistoryItemEvents(historyList);
    } catch (error) {
      console.error('加载历史列表失败:', error);
      historyList.innerHTML =
        '<div style="text-align: center; padding: 20px; color: #dc3545;">加载失败</div>';
    }
  }

  /**
   * 创建历史项目HTML
   */
  private createHistoryItemHTML(session: SessionData): string {
    const startTime = new Date(session.startTime);
    const duration = session.endTime ? session.endTime - session.startTime : 0;
    const statusText =
      session.status === 'completed'
        ? '已完成'
        : session.status === 'recording'
          ? '记录中'
          : '已中断';
    const statusColor =
      session.status === 'completed'
        ? '#28a745'
        : session.status === 'recording'
          ? '#007bff'
          : '#ffc107';

    return `
      <div class="history-item" data-session-id="${session.sessionId}">
        <div class="history-item-info">
          <div class="history-item-title">${session.metadata.title || '未知页面'}</div>
          <div class="history-item-meta">
            <span style="color: ${statusColor}">● ${statusText}</span> •
            ${startTime.toLocaleString()} •
            ${this.formatDuration(duration)}
          </div>
        </div>
        <div class="history-item-actions">
          <button class="export-btn" data-action="export" data-session-id="${session.sessionId}">导出</button>
          <button class="delete-btn" data-action="delete" data-session-id="${session.sessionId}">删除</button>
        </div>
      </div>
    `;
  }

  /**
   * 绑定历史项目事件
   */
  private bindHistoryItemEvents(historyList: HTMLElement): void {
    historyList.addEventListener('click', async (e) => {
      const target = e.target as HTMLElement;
      const action = target.getAttribute('data-action');
      const sessionId = target.getAttribute('data-session-id');

      if (!sessionId) return;

      try {
        if (action === 'export') {
          await this.exportManager.exportHistorySession(sessionId);
        } else if (action === 'delete') {
          if (confirm('确定要删除这个会话记录吗？')) {
            await this.enhancedOSpy.getSessionDB().deleteSession(sessionId);
            await this.loadHistoryList(); // 重新加载列表
          }
        }
      } catch (error) {
        console.error('操作失败:', error);
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        alert('操作失败: ' + errorMessage);
      }
    });
  }

  /**
   * 过滤历史记录
   */
  private filterHistory(query: string): void {
    if (!this.floatingPanel) return;

    const historyItems = this.floatingPanel.querySelectorAll('.history-item');
    const searchQuery = query.toLowerCase();

    historyItems.forEach((item) => {
      const title =
        item.querySelector('.history-item-title')?.textContent?.toLowerCase() ||
        '';
      const meta =
        item.querySelector('.history-item-meta')?.textContent?.toLowerCase() ||
        '';

      const matches = title.includes(searchQuery) || meta.includes(searchQuery);
      (item as HTMLElement).style.display = matches ? 'flex' : 'none';
    });
  }

  /**
   * 按状态过滤历史记录
   */
  private filterHistoryByStatus(status: string): void {
    if (!this.floatingPanel) return;

    const historyItems = this.floatingPanel.querySelectorAll('.history-item');

    historyItems.forEach((item) => {
      const sessionId = item.getAttribute('data-session-id');
      if (!sessionId) return;

      // 如果没有选择状态，显示所有
      if (!status) {
        (item as HTMLElement).style.display = 'flex';
        return;
      }

      // 根据状态文本判断
      const statusText =
        item.querySelector('.history-item-meta')?.textContent || '';
      let itemStatus = '';

      if (statusText.includes('已完成')) itemStatus = 'completed';
      else if (statusText.includes('已中断')) itemStatus = 'interrupted';
      else if (statusText.includes('记录中')) itemStatus = 'recording';

      (item as HTMLElement).style.display =
        itemStatus === status ? 'flex' : 'none';
    });
  }

  /**
   * 导出所有会话
   */
  private async exportAllSessions(): Promise<void> {
    try {
      await this.exportManager.exportAllSessions();
    } catch (error) {
      console.error('导出所有会话失败:', error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      alert('导出失败: ' + errorMessage);
    }
  }

  /**
   * 清空所有历史记录
   */
  private async clearAllHistory(): Promise<void> {
    if (!confirm('确定要清空所有历史记录吗？此操作不可恢复！')) {
      return;
    }

    try {
      const sessions = await this.enhancedOSpy.getSessionDB().getAllSessions();

      for (const session of sessions) {
        await this.enhancedOSpy.getSessionDB().deleteSession(session.sessionId);
      }

      await this.loadHistoryList();
      await this.updateHistoryStats();

      console.log('所有历史记录已清空');
    } catch (error) {
      console.error('清空历史记录失败:', error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      alert('清空失败: ' + errorMessage);
    }
  }

  /**
   * 更新历史统计信息
   */
  private async updateHistoryStats(): Promise<void> {
    if (!this.floatingPanel) return;

    const statsContainer = this.floatingPanel.querySelector(
      '.history-stats'
    ) as HTMLElement;

    try {
      const stats = await this.enhancedOSpy.getSessionDB().getStats();

      statsContainer.innerHTML = `
        <div class="stat-item">
          <span>总计:</span>
          <span class="stat-value">${stats.totalSessions}</span>
        </div>
        <div class="stat-item">
          <span>已完成:</span>
          <span class="stat-value">${stats.completedSessions}</span>
        </div>
        <div class="stat-item">
          <span>记录中:</span>
          <span class="stat-value">${stats.recordingSessions}</span>
        </div>
      `;
    } catch (error) {
      console.error('更新统计信息失败:', error);
      statsContainer.innerHTML =
        '<div class="stat-item">统计信息加载失败</div>';
    }
  }

  /**
   * 格式化持续时间
   */
  private formatDuration(ms: number): string {
    if (ms <= 0) return '0秒';

    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 切换最小化状态
   */
  private toggleMinimize(): void {
    if (!this.floatingPanel) return;

    this.isMinimized = !this.isMinimized;
    this.floatingPanel.classList.toggle('minimized', this.isMinimized);

    // 检查历史面板是否可见
    const historyPanel = this.floatingPanel.querySelector(
      '.ospy-history-panel'
    ) as HTMLElement;
    const isHistoryVisible = historyPanel && historyPanel.style.display !== 'none';

    // 根据最小化状态和历史面板可见性设置高度
    if (this.isMinimized) {
      this.floatingPanel.style.height = '50px';
    } else if (isHistoryVisible) {
      this.floatingPanel.style.height = '440px';
    } else {
      this.floatingPanel.style.height = 'auto';
    }
  }

  /**
   * 关闭面板
   */
  private closePanel(): void {
    if (this.floatingPanel) {
      this.floatingPanel.remove();
      this.floatingPanel = null;
    }
  }
}
