import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  switch (method) {
    case 'GET':
      res.status(200).json({
        session: 'mockedSessionKey',
        data: 'mockedData',
      });
      break;
    case 'POST':
    case 'PUT':
    case 'DELETE':
      res.status(200).json({
        isSuccess: true,
        error: null,
      });
      break;
    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      res.status(405).end(`Method ${method} Not Allowed`);
  }
}
