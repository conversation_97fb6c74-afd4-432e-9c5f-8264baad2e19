// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';

type DataResponse = {
  data: any;
  isSuccess: boolean;
  totalCount?: number;
  error: any;
};

const DUMMY_DATA = {
  conversationId: '-',
  startTime: '2024-03-22T02:57:28.000+0000',
  endTime: '2024-03-22T02:58:08.000+0000',
  mediaUri:
    '/recordings/E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T/play/a5d659f4-c5c7-472e-9a7a-f9dcdafc44cf.mp3',
  duration: 40.187,
  users: null,
  direction: 'Internal',
  mediaType: 'call',
  username: 'Agent 1',
  dialedNumber: '22001',
  callerNumber: '71002',
  mediaSource: 'Engage',
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<DataResponse>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  res.setHeader('Access-Control-Allow-Headers', '*');

  // Get limit and offset from query parameters
  const limit = Number(req.query.limit) || 10;
  const offset = Number(req.query.offset) || 0;

  try {
    // Generate dummy data
    const data = Array.from({ length: limit }, (_, i) => {
      const evaluation: any = {
        id: `e-${i}`,
        status: 'unassigned',
        evaluator: null,
        score: null,
      };
      if (i === 0) {
        evaluation.evaluator = 'Supervisor';
        evaluation.status = 'assigned';
      }
      if (i === 1) {
        evaluation.evaluator = 'Admin';
        evaluation.status = 'assigned';
      }
      if (i === 2) {
        evaluation.evaluator = 'Supervisor';
        evaluation.status = 'evaluated';
      }
      if (i === 3) {
        evaluation.evaluator = 'Supervisor';
        evaluation.status = 'released';
        evaluation.score = 90;
      }
      if (i === 4) {
        evaluation.evaluator = 'Supervisor';
        evaluation.status = 'released';
        evaluation.score = 60;
      }
      if (i === 5) {
        evaluation.evaluator = 'Supervisor';
        evaluation.status = 'released';
        evaluation.score = 20;
      }
      return {
        id: `E0DBMNLAM52MJ0GUEKVQ8I0ODO00000${i + offset + 1}`,
        ...DUMMY_DATA,
        evaluation,
      };
    });

    // If everything is okay, return the user data
    res.status(200).json({
      data: {
        recordings: data,
      },
      totalCount: 300,
      isSuccess: true,
      error: null,
    });
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to get recordings data',
    });
  }
}
