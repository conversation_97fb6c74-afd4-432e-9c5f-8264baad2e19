// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';

type LoginResponse = {
  id: string;
  userName: string;
  firstName: string;
  emailAddress: string;
  roles: string[];
  permissions: string[];
  bearerToken: string;
};

type DataResponse = {
  data: LoginResponse | null | string;
  isSuccess: boolean;
  error: any;
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<DataResponse>
) {
  const body = req.body;
  console.log(body);
  try {
    res.status(200).json({
      data: {
        id: 'abd5de9652b84fbcb9bf60b1168241c9',
        userName: '31001',
        firstName: '31001',
        emailAddress: '',
        roles: ['ROLE_ADMIN', 'ROLE_AGENT', 'ROLE_APIUSER', 'ROLE_SUPERVISOR'],
        permissions: [
          'ctint-mf-cpp.application.visit',
          'ctint-mf-cpp.recording.download',
          'ctint-mf-tts.application.visit',
          'ctint-mf-wap.application.visit',
          'ctint-mf-template.application.visit',
        ],
        bearerToken: 'xxxxx',
      },
      isSuccess: true,
      error: null,
    });
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to login',
    });
  }
}
