import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import { basePath, mfName } from '../../lib/appConfig';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import Toolbar from '../../components/_screen/DemoToolbar/components/Toolbar';
export const Page = () => {
  return (
    <AuthLoginChecker>
      <PageRenderer
        routes={[]}
        basePath={basePath}
      >
        <div className="w-full h-screen">
          <Toolbar />
        </div>
      </PageRenderer>
    </AuthLoginChecker>
  );
};

export const getServerSideProps = async () => {
  const globalConfig = loadGlobalConfig(mfName);
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      globalConfig,
      publicEnvVars,
    },
  };
};

export default Page;
