import Button from '@cdss-modules/design-system/components/_ui/Button';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import {
  Popup,
  PopupContent,
  PopupTrigger,
} from '@cdss-modules/design-system/components/_ui/Popup';

const PopupDemo = () => {
  return (
    <Popup>
      <PopupTrigger asChild>
        <Button variant="primary">Edit Profile</Button>
      </PopupTrigger>
      <PopupContent
        className="sm:max-w-[425px]"
        title="Add Template"
      >
        <div className="flex flex-col w-full">
          <Input />
          <div className="h-20">bla bla bla</div>
          <div className="w-full flex flex-row justify-between ">
            <Button variant="blank">Cancel</Button>
            <Button variant="primary">Add</Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

export default PopupDemo;
