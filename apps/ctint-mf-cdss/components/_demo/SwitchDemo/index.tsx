'use client';

import Switch from '@cdss-modules/design-system/components/_ui/Switch';
import { useState } from 'react';

const SwitchDemo = () => {
  const [isChecked, setIsChecked] = useState(false);
  const handleCheck = () => {
    setIsChecked(!isChecked);
  };
  return (
    <div>
      <Switch
        // checked={isChecked} //The controlled state of the switch. Must be used in conjunction with onChange.
        onChange={handleCheck}
        defaultChecked={true} //The state of the switch when it is initially rendered. Use when you do not need to control its state.
      />
    </div>
  );
};

export default SwitchDemo;
