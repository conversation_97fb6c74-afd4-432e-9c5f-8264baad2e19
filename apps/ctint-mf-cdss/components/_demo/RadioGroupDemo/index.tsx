'use client';

import { useState } from 'react';
import RadioGroup from '@cdss-modules/design-system/components/_ui/RadioGroup';

const items = [
  {
    id: 'apple',
    label: 'Apple',
    value: 'apple',
  },
  {
    id: 'orange',
    label: 'Orange',
    value: 'orange',
  },
  {
    id: 'banana',
    label: 'Banana',
    value: 'banana',
  },
];

const RadioGroupDemo = () => {
  const [fruit, setFruit] = useState<string | undefined>(undefined);

  const handleFruit = (e: any) => {
    setFruit(e.target.value);
  };

  return (
    <RadioGroup
      items={items}
      name={'fruit'}
      value={fruit}
      onChange={handleFruit}
      direction="vertical"
    />
  );
};

export default RadioGroupDemo;
