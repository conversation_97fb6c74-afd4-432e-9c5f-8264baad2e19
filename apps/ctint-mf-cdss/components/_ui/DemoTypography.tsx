import clsx from 'clsx';

interface DemoTypographyProps {
  typoKey: string;
  typoValue: string;
}
const DemoTypography = ({ typoKey, typoValue }: DemoTypographyProps) => {
  const remToApproxPx = (remString: string) => {
    const basePx = 16;
    const rem = parseFloat(remString);
    const px = Math.round(rem * basePx);
    return `${px}px`;
  };
  return (
    <div
      key={`typo-${typoKey}`}
      className={clsx(
        'w-full overflow-auto mb-4 whitespace-nowrap',
        `text-${typoKey}`
      )}
    >
      Font size as <strong>{typoKey}</strong> -{' '}
      <i>{`${typoValue} = ${remToApproxPx(typoValue as string)}`}</i>
    </div>
  );
};

export default DemoTypography;
