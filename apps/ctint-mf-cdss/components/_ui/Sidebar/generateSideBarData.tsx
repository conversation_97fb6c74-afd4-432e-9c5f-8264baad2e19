import {
  BaseConversationItem,
  CallbackConvItem,
  ConversationItem,
  EmailConvItem,
  MessageConvItem,
  VoiceConvItem,
} from '@cdss-modules/design-system/@types/Conversation';
import { TContact } from '@cdss-modules/design-system/@types/index';
export const getName = (
  conv: any,
  directoryList: TContact[] | undefined
): any => {
  let callType =
    conv?.originatingDirection || conv.eventData?.data?.agent?.[0]?.direction;
  if (
    conv?.eventData?.data?.customer &&
    conv?.eventData?.data?.customer?.length > 0
  ) {
    const sortedData = conv.eventData?.data?.customer?.sort(
      (a: any, b: any) => {
        const timeA = new Date(a?.connectedTime || a?.startTime)?.getTime();
        const timeB = new Date(b?.connectedTime || a?.startTime)?.getTime();
        return timeA - timeB;
      }
    );
    const customerData = sortedData?.[0];
    if (!callType) {
      callType = customerData?.direction;
    }
    return (
      extractNumbersFromTel(customerData?.address) ||
      (callType === 'inbound'
        ? extractNumbersFromTel(customerData?.ani)
        : extractNumbersFromTel(customerData?.dnis)) ||
      (callType === 'inbound'
        ? extractNumbersFromTel(
            conv?.eventData?.data?.agent?.[
              conv?.eventData?.data?.agent?.length - 1
            ]?.ani
          )
        : extractNumbersFromTel(
            conv?.eventData?.data?.agent?.[
              conv?.eventData?.data?.agent?.length - 1
            ]?.dnis
          )) ||
      conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
        ?.address
    );
  } else if (
    conv?.eventData?.data?.consult &&
    conv?.eventData?.data?.consult?.length > 0
  ) {
    const sortedData = conv?.eventData?.data?.consult?.sort(
      (a: any, b: any) => {
        const timeA = new Date(a?.connectedTime || a?.startTime)?.getTime();
        const timeB = new Date(b?.connectedTime || a?.startTime)?.getTime();
        return timeA - timeB;
      }
    );
    const userId = sortedData?.[sortedData?.length - 1]?.user?.id;
    const user = directoryList?.find((item) => item?.id === userId);
    if (directoryList) {
      return (
        user?.name ||
        extractNumbersFromTel(
          directoryList
            ?.find((item) => item?.id === userId)
            ?.primaryContactInfo?.find(
              (contact) => contact?.mediaType === 'PHONE'
            )?.address || 'N/A'
        ) ||
        (callType === 'inbound'
          ? extractNumbersFromTel(
              conv?.eventData?.data?.agent?.[
                conv?.eventData?.data?.agent?.length - 1
              ]?.ani
            )
          : extractNumbersFromTel(
              conv?.eventData?.data?.agent?.[
                conv?.eventData?.data?.agent?.length - 1
              ]?.dnis
            ))
      );
    } else {
      if (callType === 'inbound') {
        return extractNumbersFromTel(
          conv?.eventData?.data?.agent?.[
            conv?.eventData?.data?.agent?.length - 1
          ]?.ani
        );
      } else {
        return extractNumbersFromTel(
          conv?.eventData?.data?.agent?.[
            conv?.eventData?.data?.agent?.length - 1
          ]?.dnis||extractNumbersFromTel(
            conv?.eventData?.data?.agent?.[
              conv?.eventData?.data?.agent?.length - 1
            ]?.errorInfo?.messageParams?.destinationAddress
          )||"unknown"
        );
      }
    }
  } else {
    if (callType === 'inbound') {
      return extractNumbersFromTel(
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.ani
      );
    } else {
      return extractNumbersFromTel(
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.dnis||extractNumbersFromTel(
            conv?.eventData?.data?.agent?.[
              conv?.eventData?.data?.agent?.length - 1
            ]?.errorInfo?.messageParams?.destinationAddress
          )||"unknown"
      );
    }
  }
};
const getPhoneNumber = (
  conv: any,
  directoryList: TContact[] | undefined
): any => {
  let callType =
    conv?.eventData?.data?.agent?.[0]?.direction || conv?.originatingDirection;
  if (
    conv?.eventData?.data?.customer &&
    conv?.eventData?.data?.customer?.length > 0
  ) {
    const sortedData = conv.eventData?.data?.customer?.sort(
      (a: any, b: any) => {
        const timeA = new Date(a?.connectedTime || a?.startTime)?.getTime();
        const timeB = new Date(b?.connectedTime || a?.startTime)?.getTime();
        return timeA - timeB;
      }
    );
    const customerData = sortedData?.[0];
    if (!callType) {
      callType = customerData?.direction;
    }
    if (callType === 'inbound') {
      return (
        extractNumbersFromTel(
          conv?.eventData?.data?.agent?.[
            conv?.eventData?.data?.agent?.length - 1
          ]?.ani
        ) || extractNumbersFromTel(customerData?.ani)
      );
    } else {
      return (
        extractNumbersFromTel(
          conv?.eventData?.data?.agent?.[
            conv?.eventData?.data?.agent?.length - 1
          ]?.dnis
        ) || extractNumbersFromTel(customerData?.dnis)
      );
    }
  } else if (
    conv?.eventData?.data?.consult &&
    conv?.eventData?.data?.consult?.length > 0
  ) {
    const sortedData = conv?.eventData?.data?.consult?.sort(
      (a: any, b: any) => {
        const timeA = new Date(a?.connectedTime || a?.startTime)?.getTime();
        const timeB = new Date(b?.connectedTime || a?.startTime)?.getTime();
        return timeA - timeB;
      }
    );
    if (!callType) {
      callType = sortedData?.[sortedData?.length - 1]?.direction;
    }
    const userId = sortedData?.[sortedData?.length - 1]?.user?.id;
    if (directoryList) {
      return extractNumbersFromTel(
        directoryList
          ?.find((item) => item.id === userId)
          ?.primaryContactInfo.find((contact) => contact.mediaType === 'PHONE')
          ?.address
      );
    } else {
      if (callType === 'inbound') {
        return extractNumbersFromTel(
          conv?.eventData?.data?.agent?.[
            conv?.eventData?.data?.agent?.length - 1
          ]?.ani
        );
      } else {
        return extractNumbersFromTel(
          conv?.eventData?.data?.agent?.[
            conv?.eventData?.data?.agent?.length - 1
          ]?.dnis
        );
      }
    }
  } else {
    if (callType === 'inbound') {
      return extractNumbersFromTel(
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.ani
      );
    } else {
      return extractNumbersFromTel(
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.dnis
      );
    }
  }
};
const getUserId = (conv: any, directoryList: TContact[] | undefined): any => {
  if (
    conv?.eventData?.data?.consult &&
    conv?.eventData?.data?.consult?.length > 0
  ) {
    const userId = conv?.eventData?.data?.consult?.[0]?.user?.id;
    return userId;
  } else {
    return undefined;
  }
};
export const initConversationData = (
  activeConversationListRs: any,
  alertingConversation: any,
  directoryList?: TContact[]
): ConversationItem[] => {
  const activeConversationList: ConversationItem[] = [];
  // console.log('activeConversationListRs', activeConversationListRs);
  // console.log('alertingConversation', alertingConversation);

  const activeConversations = removeConversationFromData(
    activeConversationListRs,
    alertingConversation
  );

  // console.log('activeConversations', activeConversations);

  (activeConversations as Array<any>).map((conv) => {
    // console.log(
    //   conv.eventData.data.agent[conv.eventData.data.agent.length - 1].queue.id
    // );
    let conversation: BaseConversationItem = {
      id: conv?.id,
      userName: '',
      startTime:
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.startTime,
      endTime: '',
      type: conv?.type,
      isActive: false,
      borderProgress: 0,
      bgProgress: 0,
      icon: '',
      queueId:
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.queue?.id,
      connectedTime:
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.connectedTime,
    };
    if ('email' == conv?.type) {
      conversation = {
        ...conversation,
        userName:
          conv?.eventData?.data?.customer?.[
            conv?.eventData?.data?.customer?.length - 1
          ]?.address,
        icon: 'email',
      };
      // console.log(conversation);
      activeConversationList?.push(conversation as EmailConvItem);
    }
    if ('callback' == conv?.type) {
      let mediaType = conv?.eventData?.data?.mediaType;

      // 如果mediaType为空，检查acd数组中的voicemail字段
      if (
        !mediaType &&
        conv?.eventData?.data?.acd &&
        conv?.eventData?.data?.acd?.length > 0
      ) {
        const hasVoicemail = conv?.eventData?.data?.acd?.some(
          (acd: any) => acd?.voicemail
        );
        if (hasVoicemail) {
          mediaType = 'callback.voicemail';
        } else {
          mediaType = 'callback';
        }
      }
      const iconType =
        mediaType === 'callback.voicemail' ? 'voicemail' : 'callback';
      // console.log('mediaType', mediaType);
      // console.log('iconType', iconType);
      conversation = {
        ...conversation,
        userName: conv?.eventData?.data?.customer[
          conv?.eventData?.data?.customer?.length - 1
        ]?.address?.replace('tel:', ''),
        icon: iconType,
        mediaType: mediaType,
      };
      // console.log(conversation);
      activeConversationList.push(conversation as CallbackConvItem);
    }
    if ('call' == conv.type) {
      let type = 'call';
      let userName = '';
      // console.log(conv.eventData.data.customer[0].direction == 'inbound');
      if (
        'inbound' == conv?.eventData?.data?.agent?.[0]?.direction ||
        conv?.originatingDirection === 'inbound'
      ) {
        type = 'call';
        userName = getName(conv, directoryList);
        // userName=extractNumbersFromTel(conv?.eventData?.data?.agent[conv?.eventData?.data?.agent?.length - 1]?.ani)
      }
      if (
        'outbound' == conv?.eventData?.data?.agent?.[0]?.direction ||
        conv?.originatingDirection === 'outbound'
      ) {
        type = 'outbound';
        userName = getName(conv, directoryList);
        // userName=extractNumbersFromTel(conv?.eventData?.data?.agent[conv?.eventData?.data?.agent?.length - 1]?.dnis)
      }

      conversation = {
        ...conversation,
        userName: userName,
        icon: type,
        connectedTime:
          conv?.eventData?.data?.agent?.[
            conv?.eventData?.data?.agent?.length - 1
          ]?.startTime ||
          conv?.eventData?.data?.agent?.[
            conv?.eventData?.data?.agent?.length - 1
          ]?.connectedTime,

        //   state: conv.eventData?.data?.agent?.[0]?.state=="terminated"?"Connected":conv.eventData?.data?.agent?.[0]?.state,
      };
      activeConversationList.push(conversation as VoiceConvItem);
      //   }
    }

    if ('message' == conv.type) {
      conversation = {
        ...conversation,
        userName:
          conv?.eventData?.data?.customer?.[
            conv?.eventData?.data?.customer?.length - 1
          ]?.fromAddress?.name ||
          conv?.eventData?.data?.customer?.[
            conv?.eventData?.data?.customer?.length - 1
          ]?.fromAddress?.addressNormalized, // for new conversation data use "addressNormalized",
        icon: 'whatsapp',
        integrationId:
          conv?.eventData?.data?.customer?.[
            conv?.eventData?.data?.customer?.length - 1
          ]?.toAddress?.addressNormalized,
        originatingDirection:
          conv?.eventData?.data?.customer?.[
            conv?.eventData?.data?.customer?.length - 1
          ]?.direction,
        hasUnread: false,
        latestMessage: '',
        latestMessageTime: '',
      } as MessageConvItem;
      activeConversationList.push(conversation as MessageConvItem);
    }
    // console.log(activeConversationList);
  });
  return activeConversationList;
};

export const checkDuplicatedConversation = (
  conversation: { id: string; [key: string]: any },
  conversations: any[]
): boolean => {
  const rs = conversations?.find((conv) => conv?.id === conversation?.id);
  return !!rs;
};

const removeConversationFromData = (jsonData: any, conversation: any): any => {
  try {
    if (!jsonData?.data || !Array.isArray(jsonData?.data)) {
      return jsonData;
    }

    return jsonData?.data?.filter(
      (item: { id: string; [key: string]: any }) =>
        item?.id !== conversation?.id
    );
  } catch (error) {
    console.error('Error removing conversation:', error);
    return jsonData;
  }
};

// export const checkDuplicatedAlertingConversation = ()=> {

// }
export function extractNumbersFromTel(
  tel: string | undefined
): string | undefined {
  if (!tel) {
    return tel;
  }
  if (
    !tel?.includes('tel') &&
    !tel?.startsWith('+') &&
    !tel?.includes('sip:+')
  ) {
    return tel;
  }
  // 使用正则表达式匹配数字部分
  const match = tel?.match(/\d+/);
  if (match && match?.length > 0) {
    return match[0];
  }
  return tel;
}
export const initConversationList = (
  conversations: any,
  directoryList: TContact[] | undefined
) => {
  const conversationList: ConversationItem[] = [];  
  (conversations as Array<any>).map((conv) => {
    let icon = 'call';
    let userName = '';
    if (conv?.mediaType == 'callback') {
      icon = 'callback';
    }
    if (
      conv?.mediaType == 'voice' &&
      (conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
        ?.direction === 'inbound' ||
        conv?.originatingDirection === 'inbound')
    ) {
      icon = 'call';
      // userName=extractNumbersFromTel(conv?.eventData?.data?.agent[conv?.eventData?.data?.agent?.length - 1]?.ani)
      userName = getName(conv, directoryList);
    }
    if (
      conv?.mediaType == 'voice' &&
      (conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
        ?.direction === 'outbound' ||
        conv?.originatingDirection === 'outbound')
    ) {
      icon = 'outbound';
      userName = getName(conv, directoryList);
    }
    let conversation: BaseConversationItem = {
      id: conv?.conversationId,
      userName: '',
      startTime: conv?.conversationStart,
      endTime: conv?.conversationEnd,
      type: 'call',
      mediaType: conv?.mediaType,
      isActive: false,
      borderProgress: 0,
      bgProgress: 0,
      icon: '',
      queueId:
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.queue?.id,
      connectedTime:
        conv?.conversationStart ||
        conv?.eventData?.data?.agent?.[conv?.eventData?.data?.agent?.length - 1]
          ?.startTime,
    };
    
    if (conv?.mediaType === 'voice') {
        conversation = {
            ...conversation,
            userName: userName,
            icon: icon,
            state: conv?.missedCall ? 'Missed' : 'Connected',
            phoneNumber: getPhoneNumber(conv, directoryList),
            agentId: getUserId(conv, directoryList),
          };
      conversationList.push(conversation as VoiceConvItem);
    }
    // if ('message' == conv?.mediaType) {
    //     conversation = {
    //       ...conversation,
    //       userName:
    //         conv?.eventData?.data?.customer?.[
    //           conv?.eventData?.data?.customer?.length - 1
    //         ]?.name,
    //       icon: 'whatsapp',
    //       integrationId:
    //         conv?.eventData?.data?.customer?.[
    //           conv?.eventData?.data?.customer?.length - 1
    //         ]?.toAddress?.addressNormalized,
    //       originatingDirection:
    //         conv?.eventData?.data?.customer?.[
    //           conv?.eventData?.data?.customer?.length - 1
    //         ]?.direction,
    //       hasUnread: false,
    //       latestMessage: '',
    //       latestMessageTime: '',
    //       type: 'message',
    //     } as MessageConvItem;
    //     conversationList.push(conversation as MessageConvItem);
    //   }
    //   if ('email' == conv?.mediaType) {
        
    //     conversation = {
    //       ...conversation,
    //       userName:
    //         conv?.eventData?.data?.customer?.[
    //           conv?.eventData?.data?.customer?.length - 1
    //         ]?.address,
    //       icon: 'email',
    //       type: 'email',
    //     };
    //     // console.log(conversation);
    //     conversationList?.push(conversation as EmailConvItem);
    //   }
  });

  return conversationList;
};
