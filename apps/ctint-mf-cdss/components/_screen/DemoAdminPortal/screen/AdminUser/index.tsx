/* eslint-disable @nx/enforce-module-boundaries */

import {
  CDSSAdminProvider,
  Panel,
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminUserList from '../../ui/AdminUserList';
import AdminUserGroupList from '../../ui/AdminUserGroupList';
import AdminUserRoleList from '../../ui/AdminUserRoleList';
import AdminUserPermissionList from '../../ui/AdminUserPermissionList';
import { Filter } from 'lucide-react';
import { useState } from 'react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import AdminFilter from '../../ui/AdminFilter';

export const AdminUserBody = () => {
  const [filterOpen, setFilterOpen] = useState(false);
  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto">
      <Panel containerClassName="h-full">
        <Tabs
          defaultTab={'users'}
          triggers={[
            {
              value: 'users',
              label: 'Users',
            },
            {
              value: 'user-groups',
              label: 'User Groups',
            },
            {
              value: 'roles',
              label: 'Roles',
            },
            {
              value: 'permissions',
              label: 'Permissions',
            },
          ]}
          triggerClassName="py-2 px-2 text-body"
          rightPanel={
            <div className="flex items-center pr-2">
              <button
                className={cn(
                  'hover:text-primary',
                  filterOpen && 'text-primary'
                )}
                type="button"
                onClick={() => {
                  setFilterOpen(!filterOpen);
                }}
              >
                <Filter />
              </button>
            </div>
          }
        >
          {filterOpen && <AdminFilter />}
          <TabsContent
            value={'users'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserList />
          </TabsContent>
          <TabsContent
            value={'user-groups'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserGroupList />
          </TabsContent>
          <TabsContent
            value={'roles'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserRoleList />
          </TabsContent>
          <TabsContent
            value={'permissions'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserPermissionList />
          </TabsContent>
        </Tabs>
      </Panel>
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

const AdminUser = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <CDSSAdminProvider>
        <AdminUserBody />
      </CDSSAdminProvider>
    </QueryClientProvider>
  );
};

export default AdminUser;
