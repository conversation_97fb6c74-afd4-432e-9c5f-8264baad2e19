import { useCDSSAdmin } from '@cdss-modules/design-system';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { cn, get2LettersFromName } from '@cdss-modules/design-system/lib/utils';
import { PenSquare, Save } from 'lucide-react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { Fragment, useEffect, useState } from 'react';
import { AdminUserRoleList } from '../AdminUserRoleList';
import _ from 'lodash';
import Input from '@cdss-modules/design-system/components/_ui/Input';

export const AdminUserGroupDetail = () => {
  const { updateOpenedEntity, openedEntity } = useCDSSAdmin();
  const [isEditing, setIsEditing] = useState(false);
  const methods = useForm({
    //   resolver: yupResolver(sopSchema),
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
  } = methods;

  const onSubmit = async (data: any) => {
    if (!isEditing) {
      setIsEditing(true);
    } else {
      setIsEditing(false);
    }
  };
  const isNew = _.isEmpty(openedEntity?.entity);

  useEffect(() => {
    if (!openedEntity?.entity || isNew) {
      setIsEditing(true);
    }
  }, [openedEntity, isNew]);

  const [selectedTab, setSelectedTab] = useState('user-roles');

  const entity = openedEntity?.entity;
  const avatarLetters = (
    entity?.name ? get2LettersFromName(entity?.name || '') : 'NA'
  ) as string;
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col w-full h-full px-2 pt-0 pb-6"
      >
        <div className="flex-1 h-0 flex flex-col gap-y-4 overflow-auto">
          <div className="flex flex-col gap-x-4 w-full h-0 flex-1">
            <div className="w-full relative flex items-center gap-x-2 justify-between py-2 z-30">
              <div className="w-full flex items-center gap-x-8">
                <div className="flex items-center gap-x-2">
                  <button
                    type="button"
                    onClick={() => updateOpenedEntity(null)}
                    className="inline-flex gap-x-2 items-center group/sop-back hover:text-primary whitespace-nowrap"
                  >
                    <Icon
                      name="back"
                      //   className="hidden group-hover/sop-back:inline-flex"
                      className="inline-flex"
                    />
                    User Groups
                  </button>
                  <span className="text-black">/</span>
                  <div className="w-full">
                    <span className="text-black">
                      {entity?.name || 'New User Group'}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-x-2">
                {isEditing ? (
                  <>
                    <Button
                      size="s"
                      type="submit"
                      variant="secondary"
                      onClick={() => {
                        if (isNew) {
                          updateOpenedEntity(null);
                        } else {
                          setIsEditing(false);
                        }
                      }}
                    >
                      <span className="whitespace-nowrap">Discard</span>
                    </Button>
                    <Button
                      beforeIcon={<Save />}
                      size="s"
                      type="submit"
                    >
                      <span className="whitespace-nowrap">Save User Group</span>
                    </Button>
                  </>
                ) : (
                  <Button
                    beforeIcon={<PenSquare />}
                    size="s"
                    type="button"
                    onClick={() => setIsEditing(true)}
                  >
                    <span className="whitespace-nowrap">Edit User Group</span>
                  </Button>
                )}
              </div>
            </div>
            <div className="flex items-start py-2 gap-x-8">
              <div className="flex items-center gap-x-6">
                <div
                  className={cn(
                    'flex flex-col',
                    isEditing ? 'gap-y-6' : 'gap-y-1'
                  )}
                >
                  <Controller
                    name={'name'}
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <>
                        {isEditing ? (
                          <div className="flex gap-4">
                            <div className="text-body font-bold">Name</div>
                            <Input
                              size="s"
                              defaultValue={entity?.name}
                              className="w-full"
                              {...field}
                            />
                          </div>
                        ) : (
                          <div className="text-t5 -mt-1 font-bold">
                            {entity?.name}
                          </div>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>
            </div>
            <div
              className={cn(
                'mt-4 flex flex-col w-full h-full rounded-2xl bg-white'
              )}
            >
              <div className={cn('p-4 h-0 flex-1 flex flex-col')}>
                <AdminUserRoleList inDetail />
              </div>
            </div>
          </div>
          <div className="flex justify-between gap-y-4 pt-4 h-10 overflow-auto hidden">
            {avatarLetters}
            ##
            {JSON.stringify(entity)}
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default AdminUserGroupDetail;
