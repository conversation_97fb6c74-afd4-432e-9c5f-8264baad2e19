import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Controller, useFormContext } from 'react-hook-form';

export const EditableField = ({ isEditing, ...thisField }: any) => {
  const { control } = useFormContext();

  return (
    <div className="flex flex-wrap gap-x-6 w-[300px]">
      <Field
        title={thisField?.title}
        icon={<Icon name="error" />}
        placeholder={thisField?.placeholder || 'Please enter'}
      >
        <Controller
          name={thisField?.name || ''}
          control={control}
          rules={{ required: true }}
          render={({ field }) => (
            <>
              {isEditing ? (
                <Input
                  size="s"
                  {...field}
                  defaultValue={thisField?.value || field?.value}
                />
              ) : (
                <span>{`${thisField?.value || field?.value || 'N/A'}`}</span>
              )}
            </>
          )}
        />
      </Field>
    </div>
  );
};

export default EditableField;
