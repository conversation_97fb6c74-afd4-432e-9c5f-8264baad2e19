import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { useState } from 'react';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import {
  SortingButton,
  Tooltip,
  useCDSSAdmin,
} from '@cdss-modules/design-system';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { Plus } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';

// Types
type TAdminUserData = {
  id?: string;
  name?: string;
  username?: string;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
};

type TAdminUserDataColumn = keyof TAdminUserData;

const DICTIONARY_COLUMN_LABEL: any = {
  id: 'ID',
  name: 'Name',
  desc: 'Description',
  latestVersion: 'Latest Version',
  createdBy: 'Created By',
  createdAt: 'Created At',
  updatedBy: 'Updated By',
  updatedAt: 'Updated At',
};

const generateColumns = (
  columns: TAdminUserDataColumn[],
  columnOrdering: string[],
  sortOrder: any,
  setSortOrder: (input: any) => void,
  openEntity: (id?: string) => void
) => {
  const selectionCol = {
    id: 'select',
    header: ({ table }: any) => (
      <div
        className="bg-white z-30 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            table.toggleAllPageRowsSelected(isSelected);
          }}
        />
      </div>
    ),
    cell: ({ row }: any) => (
      <div
        className="z-0 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={row.getIsSelected()}
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            row.toggleSelected(isSelected);
          }}
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const actionCol = {
    id: 'action',
    header: () => (
      <div>
        <Tooltip
          trigger={
            <button
              type="button"
              className="bg-primary text-white rounded-full size-6 flex-none flex justify-center items-center animate-pulse hover:animate-none"
              onClick={() => {
                openEntity();
              }}
            >
              <Plus size={18} />
            </button>
          }
          content="Add New Entity"
        />
      </div>
    ),
    cell: <div className="flex gap-x-2 z-0"></div>,
    enableSorting: false,
    enableHiding: false,
  };

  const orderedColumns = columnOrdering
    ? columns.sort((firstEl, secondEl) => {
        const firstIndex = columnOrdering?.findIndex(
          (item: any) => item.name === firstEl
        );
        const secondIndex = columnOrdering?.findIndex(
          (item: any) => item.name === secondEl
        );
        return firstIndex - secondIndex;
      })
    : columns;

  const formattedColumns = orderedColumns.map((column: string) => {
    const isDate = column === 'createdAt' || column === 'updatedAt';

    return {
      id: column,
      accessorKey: column,
      header: () => {
        return (
          <SortingButton
            sorting={
              sortOrder?.[column]
                ? sortOrder?.[column] === 'ASC'
                  ? 'asc'
                  : 'desc'
                : false
            }
            onClick={async () => {
              const targetSortOrder =
                sortOrder?.[column] === 'ASC' ? 'DESC' : 'ASC';
              setSortOrder({
                [column]: targetSortOrder,
              });
            }}
          >
            {DICTIONARY_COLUMN_LABEL?.[column] || column}
          </SortingButton>
        );
      },
      cell: ({ row }) => {
        let val = row.getValue(column) as any;
        if (isDate) val = dayjs(val).format(GLOBAL_DATETIME_FORMAT);
        if (column === 'values') val = val?.map((v: any) => v.value).join(', ');
        if (column === 'desc') return <div>{val}</div>;
        if (column === 'name') return <strong>{val}</strong>;
        return <div>{val}</div>;
      },
    } as ColumnDef<TAdminUserData>;
  });

  return [selectionCol, ...formattedColumns];
};

export const AdminUserPermissionList = ({ inDetail }: any) => {
  const { permissions, openedEntity, updateOpenedEntity } = useCDSSAdmin();
  const dummyData = permissions as TAdminUserData[];
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TAdminUserData>>();
  const [sortOrder, setSortOrder] = useState<any>();
  //   const [shownColumns, setShownColumns] = useState<TSopDataColumn[]>([]);
  const shownColumns = [
    'name',
    'desc',
    'createdBy',
    'createdAt',
    'updatedBy',
    'updatedAt',
  ] as TAdminUserDataColumn[];

  const setOpenedEntity = (entity: any) => {
    if (!entity) updateOpenedEntity(null);
    updateOpenedEntity({
      type: 'permission',
      entity,
    });
  };

  return (
    <div
      className={cn(
        'flex flex-col h-full gap-y-4 overflow-auto',
        inDetail ? '-mx-4 -mt-2' : 'px-4 pt-1 pb-6 '
      )}
    >
      {/* {openedEntity && openedEntity?.type === 'permission' ? (
        <></
      ) : ( */}
      <div className="flex-1 h-0">
        <DataTable<TAdminUserData>
          data={dummyData}
          columns={
            generateColumns(
              shownColumns,
              [],
              sortOrder,
              (input) => {
                setSortOrder(input);
              },
              (id?: string) => {
                setOpenedEntity(dummyData?.find((d) => d.id === `${id}`) || {});
              }
            ) as any
          }
          loading={false}
          emptyMessage="No data found"
          // error={error?.message}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          onClickRow={(row) => {
            setOpenedEntity(dummyData?.[row?.index || 0] || {});
          }}
          onTableSetUp={(table) => setTable(table)}
        />
      </div>
      {/* )} */}
    </div>
  );
};

export default AdminUserPermissionList;
