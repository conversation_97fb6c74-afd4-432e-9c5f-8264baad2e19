/* eslint-disable import/no-anonymous-default-export */
import { Button } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';

const GJSSelect = ({ label, fieldName, ...props }) => {
  return (
    <Field
      title={label}
      icon={<Icon name="error" />}
      className="w-full"
    >
      <Select
        placeholder="placeholder"
        mode="single"
        options={[
          {
            id: 'option1',
            label: 'Option 1',
            value: 'option1',
          },
          {
            id: 'option2',
            label: 'Option 2',
            value: 'option2',
          },
          {
            id: 'option3',
            label: 'Option 3',
            value: 'option3',
          },
        ]}
        showSearch={true}
        className="pointer-events-none"
        value={undefined}
        readonly
        {...props}
      />
    </Field>
  );
};

const GJSInput = ({ label, fieldName, ...props }) => {
  return (
    <Field
      title={label}
      icon={<Icon name="error" />}
      className="w-full"
    >
      <Input
        name={fieldName}
        {...props}
      />
    </Field>
  );
};
const CdssColumn1 = ({ children }) => {
  return <div className="w-full flex p-4">{children}</div>;
};
const CdssColumn2 = () => {
  return (
    <div className="w-full flex">
      <CdssColumn1 />
      <CdssColumn1 />
    </div>
  );
};

export default (editor) => {
  editor.Components.addType('CdssColumn1', {
    extend: 'react-component',
    model: {
      defaults: {
        component: CdssColumn1,
        stylable: true,
        resizable: true,
        editable: true,
        draggable: true,
        droppable: true,
        attributes: {
          mlsid: 'CdssColumn1',
          editable: true,
        },
      },
    },
    isComponent: (el) => el.tagName === 'CDSSCOLUMN1',
  });
  editor.Components.addType('CdssColumn2', {
    extend: 'react-component',
    model: {
      defaults: {
        component: CdssColumn2,
        stylable: true,
        resizable: true,
        editable: true,
        draggable: true,
        droppable: true,
        attributes: {
          mlsid: 'CDSS_COLUMN2',
          editable: true,
        },
      },
    },
    isComponent: (el) => el.tagName === 'CDSSCOLUMN2',
  });
  editor.Components.addType('Button', {
    extend: 'react-component',
    model: {
      defaults: {
        component: Button,
        stylable: true,
        resizable: true,
        editable: true,
        draggable: true,
        droppable: true,
        attributes: {
          mlsid: 'Default MLSID',
          editable: true,
        },
        traits: [
          {
            type: 'text',
            label: 'ID',
            name: 'mlsid',
          },
          {
            type: 'text',
            name: 'ubLabel',
            label: 'Text',
          },
        ],
      },
    },
    isComponent: (el) => el.tagName === 'BUTTON',
  });
  editor.Components.addType('Input', {
    extend: 'react-component',
    model: {
      defaults: {
        component: GJSInput,
        stylable: true,
        resizable: true,
        editable: true,
        draggable: true,
        droppable: true,
        attributes: {
          mlsid: 'CDSS_INPUT',
          editable: true,
        },
        traits: [
          {
            type: 'text',
            label: 'ID',
            name: 'mlsid',
          },
          {
            type: 'text',
            name: 'fieldName',
            label: 'Field Name',
          },
          {
            type: 'text',
            name: 'label',
            label: 'Label',
          },
          {
            type: 'text',
            name: 'placeholder',
            label: 'Placeholder',
          },
          {
            type: 'checkbox',
            label: 'Required',
            name: 'validation',
            valueTrue: true, // Value to assign when is checked, default: `true`
            valueFalse: false, // Value to assign when is unchecked, default: `false`
          },
        ],
      },
    },
    isComponent: (el) => el.tagName === 'INPUT',
  });
  editor.Components.addType('Select', {
    extend: 'react-component',
    model: {
      defaults: {
        component: GJSSelect,
        stylable: true,
        resizable: true,
        editable: true,
        draggable: true,
        droppable: true,
        attributes: {
          mlsid: 'CDSS_Select',
          editable: true,
        },
        traits: [
          {
            type: 'text',
            label: 'ID',
            name: 'mlsid',
          },
          {
            type: 'text',
            name: 'fieldName',
            label: 'Field Name',
          },
          {
            type: 'text',
            name: 'label',
            label: 'Label',
          },
          {
            type: 'text',
            name: 'placeholder',
            label: 'Placeholder',
          },
          {
            type: 'checkbox',
            label: 'Required',
            name: 'validation',
            valueTrue: true, // Value to assign when is checked, default: `true`
            valueFalse: false, // Value to assign when is unchecked, default: `false`
          },
        ],
      },
    },
    isComponent: (el) => el.tagName === 'SELECT',
  });

  // editor.BlockManager.add('cdss_column2', {
  //   label: 'CDSS Column2',
  //   category: 'CDSS Components',
  //   media: `<div class="w-10 h-10"><svg viewBox="0 0 23 24">
  //   <path fill="currentColor" d="M2 20h8V4H2v16Zm-1 0V4a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1ZM13 20h8V4h-8v16Zm-1 0V4a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1h-8a1 1 0 0 1-1-1Z"/>
  // </svg></div>`,
  //   content: `
  //   <CdssColumn2 />`,
  // });

  editor.BlockManager.add('cdss_button', {
    label: 'CDSS Button',
    category: 'CDSS Components',
    media:
      '<div class="font-bold border p-2 bg-black text-white rounded-lg mb-2">CDSS</div>',
    content: '<Button>Label</Button>',
  });
  editor.BlockManager.add('cdss_input', {
    label: 'CDSS Input',
    media:
      '<div class="font-bold border p-2 bg-black text-white rounded-lg mb-2">CDSS</div>',
    category: 'CDSS Components',
    content: '<Input placeholder="Placeholder" />',
  });
  editor.BlockManager.add('cdss_select', {
    label: 'CDSS Select',
    media:
      '<div class="font-bold border p-2 bg-black text-white rounded-lg mb-2">CDSS</div>',
    category: 'CDSS Components',
    content: '<Select placeholder="Please select" />',
  });
};
