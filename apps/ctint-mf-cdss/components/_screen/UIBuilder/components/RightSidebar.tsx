import * as React from 'react';
import {
  BlocksProvider,
  LayersProvider,
  PagesProvider,
  TraitsProvider,
} from '@grapesjs/react';
// import {
//   mdiBrush,
//   mdiLayers,
//   mdiViewGridPlus,
//   mdiTextBoxMultiple,
//   mdiCog,
// } from '@mdi/js';
// import Icon from '@mdi/react';
// import Tab from '@mui/material/Tab';
// import Tabs from '@mui/material/Tabs';
import CustomBlockManager from './CustomBlockManager';
import { MAIN_BORDER_COLOR, cx } from './common';
import CustomPageManager from './CustomPageManager';
import CustomLayerManager from './CustomLayerManager';
import CustomTraitManager from './CustomTraitManager';
import {
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system/components/_ui/Tabs';

const defaultTabProps = {
  className: '!min-w-0',
};

const triggers = [
  {
    value: '1',
    label: 'Traits',
  },
  {
    value: '3',
    label: 'Block',
  },
];
export default function RightSidebar({
  className,
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cx('gjs-right-sidebar bg-white flex flex-col', className)}>
      <Tabs
        triggers={triggers}
        defaultTab={'3'}
        className="text-black"
      >
        <div
          className={cx(
            'overflow-y-auto flex-grow border-t',
            MAIN_BORDER_COLOR
          )}
        >
          {/* {selectedTab === 0 && (
            <>
              <SelectorsProvider>
                {(props) => <CustomSelectorManager {...props} />}
              </SelectorsProvider>
              <StylesProvider>
                {(props) => <CustomStyleManager {...props} />}
              </StylesProvider>
            </>
          )} */}
          <TabsContent value={'1'}>
            <TraitsProvider>
              {(props) => <CustomTraitManager {...props} />}
            </TraitsProvider>
          </TabsContent>
          <TabsContent value={'2'}>
            <LayersProvider>
              {(props) => <CustomLayerManager {...props} />}
            </LayersProvider>
          </TabsContent>
          <TabsContent value={'3'}>
            <BlocksProvider>
              {(props) => <CustomBlockManager {...props} />}
            </BlocksProvider>
          </TabsContent>
          <TabsContent value={'4'}>
            <PagesProvider>
              {(props) => <CustomPageManager {...props} />}
            </PagesProvider>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
