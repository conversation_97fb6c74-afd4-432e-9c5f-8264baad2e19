import { useRole } from '@cdss-modules/design-system';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';

const ToolbarAgentInfo = () => {
  const { userConfig } = useRole();
  const {
    stationContext: { station },
  } = useTbarContext();
  return (
    <div className="w-full h-full p-1 text-remark bg-white overflow-y-auto flex flex-col gap-y-2">
      <div>
        Account: <span className="font-bold">{userConfig?.userName}</span>
      </div>
      <div>
        Workstation:{' '}
        <span className="font-bold">{station?.name || 'No station'}</span>
      </div>
    </div>
  );
};

export default ToolbarAgentInfo;
