import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { cn } from '@cdss-modules/design-system/lib/utils';

export type TToolbarModalProps = {
  id: string;
  coverArea?: 'full' | 'left' | 'right' | 'bottom';
  children: React.ReactNode;
};

const ToolbarModal = ({
  id,
  coverArea = 'full',
  children,
}: TToolbarModalProps) => {
  const { activeModal, closeToolbarModal } = useTbarContext();
  const isOpen = activeModal === id;
  return (
    <div
      id={id}
      className={cn(
        'absolute z-40 left-0 top-0 w-full h-full transition-all duration-200 flex items-center pointer-events-none',
        coverArea === 'full' && !isOpen && '-translate-y-full',
        coverArea === 'left' && !isOpen && '-translate-x-full justify-start',
        coverArea === 'right' && !isOpen && 'translate-x-full justify-end',
        coverArea === 'bottom' && !isOpen && 'translate-y-full',
        coverArea === 'full' && 'pb-8',
        coverArea === 'left' && 'justify-start',
        coverArea === 'right' && 'justify-end',
        coverArea === 'bottom' && 'pt-8',
        isOpen && 'translate-0'
      )}
    >
      <div
        className={cn(
          'relative flex z-10 h-full bg-white overflow-hidden pointer-events-auto shadow-lg',
          coverArea === 'full' && 'w-full flex-col',
          coverArea === 'left' && 'w-4/5 sm:w-3/5 lg:w-2/5 rounded-r-lg',
          coverArea === 'right' &&
            'w-4/5 sm:w-3/5 lg:w-2/5 rounded-l-lg flex-row-reverse',
          coverArea === 'left' &&
            id === 'behalf-panel' &&
            'w-1/5 sm:w-2/5 lg:w-2/5 rounded-r-lg',
          coverArea === 'bottom' && 'w-2/3 flex-col-reverse',
          coverArea === 'right' && id === 'customer-info-panel' && '!w-1/2'
        )}
      >
        <div
          className={cn('w-full h-full', coverArea === 'full' ? 'p-2' : 'p-4')}
        >
          {children}
        </div>
        <button
          className="flex items-center justify-center p-2 bg-primary-200"
          onClick={closeToolbarModal}
        >
          <div
            className={cn(
              'w-0 h-0 border-t-[8px] border-t-transparent border-r-[8px] border-r-black border-b-[8px] border-b-transparent',
              coverArea === 'full' && 'rotate-90',
              coverArea === 'right' && 'rotate-180',
              coverArea === 'bottom' && '-rotate-90'
            )}
          />
        </button>
      </div>
    </div>
  );
};

export default ToolbarModal;
