'use client';

import { But<PERSON>, useRouteHand<PERSON> } from '@cdss-modules/design-system';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';
import { logout } from '@cdss-modules/design-system/lib/utils';

const ToolbarCallSettings = () => {
  const { basePath } = useRouteHandler();

  return (
    <div className="flex flex-col gap-4">
      {/* <div className="flex items-center gap-x-2">
        <div>Is WebRTC:</div>
        <Switch
          id="queue"
          size="s"
          activeColor="green"
          checked={isWebRTC}
          onChange={() => updateIsWebRTC(!isWebRTC)}
        />
      </div> */}
      <Button
        onClick={() => {
          logout(basePath);
        }}
        size="s"
      >
        Logout
      </Button>
    </div>
  );
};

export default ToolbarCallSettings;
