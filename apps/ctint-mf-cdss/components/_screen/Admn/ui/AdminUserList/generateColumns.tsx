import { SortingButton } from '@cdss-modules/design-system';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { Tooltip } from '@cdss-modules/design-system/components/_ui/Tooltip';
import { Plus } from 'lucide-react';
import { formatDateTime } from '.';
import { ColumnDef } from '@tanstack/react-table';
import { TAdminUserData } from '../../../../../types/microfrontendsConfig';
import { useRole } from '@cdss-modules/design-system';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';

export const generateColumns = (
  columns: string[],
  showColumns: string[],
  columnOrdering: string[],
  sortOrder: any,
  setSortOrder: (input: any) => void,
  openEntity: (id?: string) => void
) => {
  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const selectionCol = {
    id: 'select',
    header: ({ table }: any) => (
      <div
        className="bg-white z-30 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        {/* <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onChange={(e: any) => {
            console.log(123);
            const isSelected = e?.target.checked;
            table.toggleAllPageRowsSelected(isSelected);
          }}
        /> */}
      </div>
    ),
    cell: ({ row }: any) => (
      <div
        className="z-0 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        {/* <Checkbox
          checked={row.getIsSelected()}
          onChange={(e: any) => {
            console.log(123);
            const isSelected = e?.target.checked;
            row.toggleSelected(isSelected);
            // console.log(row);
          }}
        /> */}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const actionCol = {
    id: 'action',
    maxSize:50,
    header: () => (
      <div className="flex justify-start">
        {new CommonPermission(globalConfig, permissions).isPermissionEnabled(
          'ctint-mf-admin',
          'user',
          'create'
        ) && (
          <Tooltip
            trigger={
              <button
                type="button"
                className="bg-primary text-white rounded-full size-6 flex-none flex justify-center items-center animate-pulse hover:animate-none"
                onClick={() => {
                  // alert(123);
                  openEntity();
                }}
              >
                <Plus size={18} />
              </button>
            }
            content="Add New Entity"
          />
        )}
      </div>
    ),
    cell: (
      <div className="flex gap-x-2 z-0 justify-end">
        {/* <AdminActionMenu /> */}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const orderedColumns = columnOrdering
    ? columns.sort((firstEl, secondEl) => {
        const firstIndex = columnOrdering?.findIndex(
          (item: any) => item.name === firstEl
        );
        const secondIndex = columnOrdering?.findIndex(
          (item: any) => item.name === secondEl
        );
        return firstIndex - secondIndex;
      })
    : columns;

  const formattedColumns = orderedColumns.map(
    (customColumn: string, index: number) => {
      const isDate =
        customColumn === 'createTime' || customColumn === 'updateTime';

      return {
        id: customColumn,
        accessorKey: customColumn,
        header: ({ column }: any) => {
          //console.log(column);
          const columnGetToggleSortingHandler =
            column.getToggleSortingHandler();
          return (
            <SortingButton
              sorting={
                sortOrder?.[customColumn]
                  ? sortOrder?.[customColumn] === 'ASC'
                    ? 'asc'
                    : 'desc'
                  : false
              }
              onClick={async (e) => {
                // column.getToggleSortingHandler();
                columnGetToggleSortingHandler?.(e);
                const targetSortOrder =
                  sortOrder?.[customColumn] === 'ASC' ? 'DESC' : 'ASC';
                //console.log(column + targetSortOrder);
                setSortOrder({
                  [customColumn]: targetSortOrder,
                });
              }}
            >
              {showColumns[index]}
            </SortingButton>
            // <div onClick={column.getToggleSortingHandler()}>
            //   {customColumn + column.getIsSorted()}
            // </div>
          );
        },
        cell: ({ row }: any) => {
          // console.log(customColumn);

          let val = row.getValue(customColumn) as any;
          //console.log(val);
          if (isDate) {
            //console.log(val);
            val = val ? formatDateTime(val) : '';
            //val = dayjs(val).format(GLOBAL_DATETIME_FORMAT);
          }
          // if (customColumn === 'values')
          //   val = val?.map((v: any) => v.value).join(', ');
          if (customColumn === 'state') {
            // console.log(val);
            // val = 'abc';
          }
          if (customColumn === 'name') return <strong>{val}</strong>;
          return <>{val != '' && val != undefined ? val : '——'}</>;
        },
        ...(isDate && {
          filterFn: (row: any, columnId: any, filterValue: any) => {
            const cellDate = new Date(row.getValue(columnId));
            // console.log(filterValue.start, filterValue.end);
            const startDate = new Date(filterValue.start);
            const endDate = new Date(filterValue.end);

            if (startDate && endDate) {
              return cellDate >= startDate && cellDate <= endDate;
            } else if (startDate) {
              return cellDate >= startDate;
            } else if (endDate) {
              return cellDate <= endDate;
            }
            return true;
          },
        }),
        ...(customColumn === 'state' && {
          filterFn: (row: any, columnId: any, filterValue: any) => {
            return row.getValue(columnId) === filterValue ? true : false;
          },
        }),
      } as unknown as ColumnDef<TAdminUserData>;
    }
  );

  return [actionCol, ...formattedColumns];
};
