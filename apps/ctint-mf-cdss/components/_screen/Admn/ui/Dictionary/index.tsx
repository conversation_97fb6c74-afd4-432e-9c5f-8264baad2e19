import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { useState } from 'react';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import { SortingButton, toast, Tooltip } from '@cdss-modules/design-system';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { Files, Plus } from 'lucide-react';
// import { DUMMY_DICTIONARY } from '../../dummy';

// Types
type TDictionaryValue = {
  type: 'text' | 'regex';
  value: string;
};
type TDictionary = {
  id?: string;
  entityKey?: string;
  name?: string;
  values?: TDictionaryValue[];
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
};

type TDictionaryColumn = keyof TDictionary;

const DICTIONARY_COLUMN_LABEL: any = {
  id: 'ID',
  name: 'Name',
  entityKey: 'Entity Key',
  values: 'Values',
  createdBy: 'Created By',
  createdAt: 'Created At',
  updatedBy: 'Updated By',
  updatedAt: 'Updated At',
};

const generateColumns = (
  columns: TDictionaryColumn[],
  columnOrdering: string[],
  sortOrder: any,
  setSortOrder: (input: any) => void,
  openEntity: (id?: string) => void
) => {
  const selectionCol = {
    id: 'select',
    header: ({ table }: any) => (
      <div
        className="bg-white z-30 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            table.toggleAllPageRowsSelected(isSelected);
          }}
        />
      </div>
    ),
    cell: ({ row }: any) => (
      <div
        className="z-0 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={row.getIsSelected()}
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            row.toggleSelected(isSelected);
          }}
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const actionCol = {
    id: 'action',
    header: () => (
      <div>
        <Tooltip
          trigger={
            <button
              type="button"
              className="bg-primary text-white rounded-full size-6 flex-none flex justify-center items-center animate-pulse hover:animate-none"
              onClick={() => {
                openEntity();
              }}
            >
              <Plus size={18} />
            </button>
          }
          content="Add New Entity"
        />
      </div>
    ),
    cell: (
      <div className="flex gap-x-2 z-0">
        <></>
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const orderedColumns = columnOrdering
    ? columns.sort((firstEl, secondEl) => {
        const firstIndex = columnOrdering?.findIndex(
          (item: any) => item.name === firstEl
        );
        const secondIndex = columnOrdering?.findIndex(
          (item: any) => item.name === secondEl
        );
        return firstIndex - secondIndex;
      })
    : columns;

  const formattedColumns = orderedColumns.map((column: string) => {
    const isDate = column === 'createdAt' || column === 'updatedAt';

    return {
      id: column,
      accessorKey: column,
      header: () => {
        return (
          <SortingButton
            sorting={
              sortOrder?.[column]
                ? sortOrder?.[column] === 'ASC'
                  ? 'asc'
                  : 'desc'
                : false
            }
            onClick={async () => {
              const targetSortOrder =
                sortOrder?.[column] === 'ASC' ? 'DESC' : 'ASC';
              setSortOrder({
                [column]: targetSortOrder,
              });
            }}
          >
            {DICTIONARY_COLUMN_LABEL?.[column] || column}
          </SortingButton>
        );
      },
      cell: ({ row }) => {
        let val = row.getValue(column) as any;
        if (isDate) val = dayjs(val).format(GLOBAL_DATETIME_FORMAT);
        if (column === 'values')
          val = val?.map((v: TDictionaryValue) => v.value).join(', ');
        if (column === 'entityKey') {
          return (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toast({
                  variant: 'success',
                  title: 'Text Copied',
                  description: `"${val}" copied to clipboard`,
                });
              }}
              className="text-primary-900 font-bold flex items-center gap-x-2 group/table-copy"
            >
              {val}
              <Files
                size={16}
                className="opacity-0 group-hover/table-copy:opacity-100"
              />
            </button>
          );
        }
        if (column === 'entityKey' || column === 'values')
          return <div className="text-primary-900 font-bold">{val}</div>;
        return <div>{val}</div>;
      },
    } as ColumnDef<TDictionary>;
  });

  return [selectionCol, ...formattedColumns, actionCol];
};

// const loginSchema = yup
//   .object({
//     username: yup.string().required('Username is required'),
//   })
//   .required();

export const Dictionary = () => {
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TDictionary>>();
  const [sortOrder, setSortOrder] = useState<any>();
  const [openedEntity, setOpenedEntity] = useState<TDictionary | null>(null);
  //   const [shownColumns, setShownColumns] = useState<TDictionaryColumn[]>([]);
  const shownColumns = [
    'name',
    'entityKey',
    'values',
    'createdBy',
    'createdAt',
    'updatedBy',
    'updatedAt',
  ] as TDictionaryColumn[];

  const dummyDictionary: any[] = [];

  return (
    <div className="px-6 pt-1 pb-6 flex flex-col h-full gap-y-4 overflow-auto">
      <div className="flex-1 h-0">
        <DataTable<TDictionary>
          data={dummyDictionary}
          columns={
            generateColumns(
              shownColumns,
              [],
              sortOrder,
              (input) => {
                setSortOrder(input);
              },
              (id?: string) => {
                setOpenedEntity(
                  dummyDictionary?.find((d) => d.id === `${id}`) || {}
                );
              }
            ) as any
          }
          loading={false}
          // error={error?.message}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          onClickRow={(row) => {
            //   toPath(`/detail?id=${row.getValue('id')}`);
          }}
          onTableSetUp={(table) => setTable(table)}
        />
      </div>
    </div>
  );
};

export default Dictionary;
