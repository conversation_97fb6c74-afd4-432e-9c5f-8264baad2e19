export const DUMMY_META = [
  {
    id: 'dummy-meta-1',
    metaKey: 'customer_type',
    name: 'Customer Type',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-2',
    metaKey: 'customer_full_name',
    name: 'Customer Full Name',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-3',
    metaKey: 'order_type',
    name: 'Order Type',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-4',
    metaKey: 'date_dob',
    name: 'Date: DOB',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-5',
    metaKey: 'number_card_no',
    name: 'Number: Card No',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-6',
    metaKey: 'number_account_no',
    name: 'Number: Account No',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-7',
    metaKey: 'number_phone_no',
    name: 'Number: Phone No',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-8',
    metaKey: 'address',
    name: 'Address',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-9',
    metaKey: 'account_owner',
    name: 'Account Owner',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-10',
    metaKey: 'online_bank_login_id',
    name: 'Online Bank Login ID',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-11',
    metaKey: 'recording_date',
    name: 'Recording Date',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-meta-12',
    metaKey: 'staff_name',
    name: 'Staff Name',
    importedBy: 'System',
    importedAt: '2024-07-06T00:00:00.000Z',
  },
];

export const DUMMY_DICTIONARY = [
  {
    id: 'dummy-entity-1',
    entityKey: 'customer_type',
    name: 'Customer Type',
    values: [
      { type: 'text', value: 'Personal' },
      { type: 'text', value: 'Corporate' },
    ],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-entity-2',
    entityKey: 'order_type',
    name: 'Order Type',
    values: [
      { type: 'text', value: 'Face-to-face' },
      { type: 'text', value: 'Phone' },
    ],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-entity-3',
    entityKey: 'dict_personal',
    name: 'Dict_Personal',
    values: [
      { type: 'text', value: '您' },
      { type: 'text', value: '貴公司' },
    ],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-entity-4',
    entityKey: 'dict_person_title',
    name: 'Dict_Person_Title',
    values: [
      { type: 'text', value: '先生' },
      { type: 'text', value: '小姐' },
    ],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-entity-5',
    entityKey: 'dict_confirmation',
    name: 'Dict_Confirmation',
    values: [
      { type: 'text', value: '正確' },
      { type: 'text', value: '不正確' },
    ],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-entity-6',
    entityKey: 'card_no',
    name: 'Card No',
    values: [{ type: 'regex', value: '\\d+' }],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-entity-7',
    entityKey: 'account_no',
    name: 'Account No',
    values: [{ type: 'regex', value: '\\d+' }],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-entity-8',
    entityKey: 'online_bank_login_id',
    name: 'Online Bank Login ID',
    values: [{ type: 'regex', value: '[a-zA-Z0-9]+' }],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-entity-9',
    entityKey: 'br_ci',
    name: 'BR_CI',
    values: [{ type: 'regex', value: '[a-zA-Z0-9]+' }],
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
];

export const DUMMY_SOP = [
  {
    id: 'sop-1',
    latestVersion: 'v2024-Sep',
    sop: {
      participants: [
        {
          id: 'agent',
          name: 'Agent',
        },
        {
          id: 'client',
          name: 'Client',
        },
      ],
      categories: [
        {
          id: 'category-1',
          name: '介紹',
          steps: [
            {
              name: '確認是否已經開始錄音。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '就%_Dict_Personal_%打算認購的股票掛鈎產品開始錄音',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '提供日期和員工姓名。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '你好, 今日是 {{recording_date}}, 我是銀行職員{{staff_name}}',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
          ],
        },
        {
          id: 'category-2',
          name: '身份驗證',
          steps: [
            {
              name: '要求客戶提供全名。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '%_dict_person_title_%， 請你提供全名',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '要求客戶提供身份證號碼後四位。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '%_dict_person_title_%， 請你提供身分證',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '要求客戶提供出生日期和住址。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '%_dict_person_title_%， 請你提供出生日期和住址。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
          ],
        },
        {
          id: 'category-3',
          name: '弱勢客戶評估',
          steps: [
            {
              name: '確認客戶是否有其他投資經驗。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '%_dict_person_title_%， 請問你是否有其他投資經驗。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認客戶是否非弱勢客戶。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '有關您的弱勢客戶評估，你的評估結果為非弱勢客戶。請確認你明白評估目的及同意評估結果。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
          ],
        },
        {
          id: 'category-4',
          name: '客戶見證人',
          steps: [
            {
              name: '確認見證人的全名。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請客戶見證人提供全名。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認見證人是否65歲以下。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請確認你是65歲以下。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認見證人是否具有中學程度或以上的教育水平。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請確認你具有中學程度或以上的教育水平。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認見證人對產品有足夠理解。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '請確認你對此產品有認識及有足夠理 解能力協助您了解此交易。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
          ],
        },
        {
          id: 'category-5',
          name: '被授權人下單',
          steps: [
            {
              name: '確認被授權人的全名。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請客戶授權人提供全名。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認被授權人對產品和衍生工具有認識。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請授權人確認對產品和衍生工具有認識。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
          ],
        },
        {
          id: 'category-6',
          name: 'PICOP 類別',
          steps: [
            {
              name: '確認客戶是否非首次購買此類產品。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請問你是否非首次購買此類產品。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認客戶已有此類投資產品的經驗。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請問你是否已有此類投資產品的經驗。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
          ],
        },
        {
          id: 'category-7',
          name: '產品適合性評估',
          steps: [
            {
              name: '確認客戶是否有足夠時間考慮投資適合性。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請問你是否有足夠時間考慮投資適合性。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認客戶是否理解此產品的特質、運作模式及相關風險。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '請問你是否理解此產品的特質、運作模式及相關風險。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認客戶是否接受潛在損失。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請問你是否接受潛在損失。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
          ],
        },
        {
          id: 'category-8',
          name: '投資評估與最終確認',
          steps: [
            {
              name: '確認客戶同意適合性評估結果。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請問你是否同意適合性評估結果。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認交易金額和戶口號碼。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請確認交易金額和戶口號碼。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認客戶是否接受交易條件並願意進行。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請問你是否接受交易條件並願意進行。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認客戶是否了解申請一旦接受便不可撤回。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請問你是否了解申請一旦接受便不可撤回。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
            {
              name: '確認是否已通知客戶產品的發行結果。',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請問你是否已收到客戶產品的發行結果通知。',
                      relation: 'similarity',
                      relationFactor: '90',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: {
                    type: 'all',
                  },
                },
              },
            },
          ],
        },
      ],
      name: 'Call Center QM SOP',
      description:
        'SOP for call center quality monitoring based on the QM form.',
    },
    createdBy: 'System',
    createdAt: '2024-09-16T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-09-16T00:00:00.000Z',
  },
  {
    id: 'dummy-sop-1',
    latestVersion: 'v2024-Jul',
    sop: {
      participants: [
        {
          id: 'agent',
          name: 'Agent',
        },
        {
          id: 'client',
          name: 'Client',
        },
      ],
      categories: [
        {
          id: 'category-1',
          name: 'Stage 1',
          steps: [
            {
              name: 'Step 1',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '你好, 今日是 {{recording_date}}, 就%_Dict_Personal_%打算認購的股票掛鈎產品開始錄音\n我是銀行職員{{staff_name}}',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'metadata',
                      dataValue: '',
                      relation: 'exists',
                      dataKey: '{{recording_date}}',
                    },
                    {
                      dataType: 'metadata',
                      dataValue: '',
                      relation: 'exists',
                      dataKey: '{{staff_name}}',
                    },
                  ],
                  passingRules: [],
                  extractions: [],
                  criteriaPassingRules: { type: 'all' },
                },
              },
            },
          ],
        },
        {
          id: 'category-0.0781527866084839',
          name: 'Stage 2',
          steps: [
            {
              name: 'Step 2-1a',
              participants: {
                agent: {
                  criteria: [
                    {
                      id: 'category-0.30880087670442524',
                      name: '',
                      steps: [],
                      type: 'metadata',
                      relation: 'equals',
                      key: 'customer_type',
                      dataValue: 'Personal',
                    },
                    {
                      id: 'category-0.2018268171385067',
                      name: '',
                      steps: [],
                      type: 'metadata',
                      relation: 'equals',
                      key: 'order_type',
                      dataValue: 'face-to-face',
                    },
                  ],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue:
                        '%_dict_person_title_%， 您全名是{{customer_full_name}}',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'metadata',
                      dataValue: '',
                      relation: 'exists',
                      dataKey: '{{customer_full_name}}',
                    },
                    {
                      dataType: 'dict',
                      dataValue: '',
                      relation: 'exists',
                      dataKey: '%_dict_confirmation_%',
                    },
                  ],
                  passingRules: [],
                  extractions: [
                    {
                      id: 'extractions-0.894671902720888',
                      type: 'dict',
                      key: 'dict_confirmation',
                    },
                  ],
                  criteriaPassingRules: { type: 'all' },
                },
              },
            },
            {
              name: 'Step 2-b',
              participants: {
                agent: {
                  criteria: [
                    {
                      id: 'category-0.23770958666413433',
                      name: '',
                      steps: [],
                      type: 'metadata',
                      relation: 'equals',
                      key: 'customer_type',
                      dataValue: 'Personal',
                    },
                    {
                      id: 'category-0.25143120171694355',
                      name: '',
                      steps: [],
                      type: 'metadata',
                      relation: 'equals',
                      key: 'order_type',
                      dataValue: 'Phone',
                    },
                  ],
                  verificationRules: [
                    {
                      dataType: 'text',
                      dataValue: '請您提供全名、身份證號碼後面的4 位數字',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'text',
                      dataValue: '請您提供出生日期（日子及月份)',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'text',
                      dataValue: '請您提供本行的任何自動櫃員機卡/户口號碼',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'text',
                      dataValue: '請您提供公司電話、住宅電話或手提電話',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'text',
                      dataValue: '請您提供住址、公司地址或任何户口的通訊地址',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'text',
                      dataValue: '請您提供本行的任何信用卡號碼',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'text',
                      dataValue: '請您提供本行的任何聯名戶口持有人的全名',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'text',
                      dataValue:
                        '請您提供本行的網上銀行客戶名稱的頭4 個字母或數字',
                      relation: 'exists',
                      relationFactor: '90',
                    },
                    {
                      dataType: 'rule-set',
                      dataValue: '',
                      relation: 'numberof',
                      ruleSet: [
                        'Rule V2',
                        'Rule V3',
                        'Rule V8',
                        'Rule V7',
                        'Rule V6',
                        'Rule V5',
                        'Rule V4',
                      ],
                      relationFactor: '2',
                    },
                  ],
                  passingRules: [],
                  extractions: [
                    {
                      id: 'extractions-0.****************',
                      type: 'metadata',
                      key: 'date_dob',
                    },
                    {
                      id: 'extractions-0.****************',
                      type: 'metadata',
                      key: 'number_card_no',
                    },
                    {
                      id: 'extractions-0.****************',
                      type: 'metadata',
                      key: 'number_account_no',
                    },
                    {
                      id: 'extractions-0.****************',
                      type: 'metadata',
                      key: 'number_phone_no',
                    },
                    {
                      id: 'extractions-0.*****************',
                      type: 'metadata',
                      key: 'address',
                    },
                    {
                      id: 'extractions-0.****************',
                      type: 'metadata',
                      key: 'account_owner',
                    },
                    {
                      id: 'extractions-0.****************',
                      type: 'metadata',
                      key: 'online_bank_login_id',
                    },
                  ],
                  criteriaPassingRules: { type: 'all' },
                },
              },
            },
          ],
        },
      ],
      name: 'ELI Public Offer',
      description: 'SOP for ELI Public Offer',
    },
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-sop-2',
    latestVersion: 'v2024-Jul',
    sop: {
      categories: [],
      name: 'FX Linked Deposit – High Yield Deposit (HYD)',
      participants: [
        {
          id: 'agent',
          name: 'Agent',
        },
        {
          id: 'client',
          name: 'Client',
        },
      ],
    },
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-sop-3',
    latestVersion: 'v2023-Dec',
    sop: {
      categories: [],
      name: 'FX Linked Deposit – Principal Protected Deposit (PPD)',
      participants: [
        {
          id: 'agent',
          name: 'Agent',
        },
        {
          id: 'client',
          name: 'Client',
        },
      ],
    },
    createdBy: 'System',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
];

export const DUMMY_RULES = [
  {
    id: 'dummy-rule-1',
    key: 'GR-1',
    name: '說出招呼語',
    dataType: 'text',
    dataValue: '%_dict_person_title_%， 您全名是{{customer_full_name}}',
    relation: 'exists',
    relationFactor: '90',
    createdBy: 'User',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-rule-2',
    key: 'GR-2',
    name: '提到正確的顧客全名',
    dataType: 'metadata',
    dataValue: '',
    relation: 'exists',
    dataKey: '{{customer_full_name}}',
    createdBy: 'User',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-rule-2_1',
    key: 'GR-2',
    name: '提到正確的員工全名',
    dataType: 'metadata',
    dataValue: '',
    relation: 'exists',
    dataKey: '{{staff_full_name}}',
    createdBy: 'User',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-rule-4',
    key: 'GR-4',
    name: '提到確認字眼',
    dataType: 'rule-set',
    dataValue: '',
    relation: 'all',
    ruleSet: ['GR-1', 'GR-2'],
    relationFactor: '2',
    createdBy: 'User',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
];

export const DUMMY_TOPICS = [
  {
    id: 'dummy-topic-1',
    key: 'TP-1',
    name: '打招呼',
    data: [
      {
        type: 'text',
        value: '先生，您全名是陳大文',
      },
      {
        type: 'text',
        value: '小姐，您全名叫黃一心',
      },
      {
        type: 'text',
        value: '小姐，您個名係黃一心',
      },
    ],
    createdBy: 'User',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
  {
    id: 'dummy-topic-2',
    key: 'TP-2',
    name: '確認',
    data: [
      {
        type: 'text',
        value: '先生，您全名是陳大文',
      },
    ],
    createdBy: 'User',
    createdAt: '2024-07-05T00:00:00.000Z',
    updatedBy: 'User',
    updatedAt: '2024-07-06T00:00:00.000Z',
  },
];
