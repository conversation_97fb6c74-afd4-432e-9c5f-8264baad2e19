import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import {
  Controller,
  FormProvider,
  useFieldArray,
  useForm,
} from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button } from '@cdss-modules/design-system';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import EditableInput from '../EditableInput';
import { Plus, Trash2, Upload } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';

export type TTopicFormProps = {
  data: any;
  onOpenChange: (open: boolean) => void;
};
// Types
export type TSOPTopicExample = {
  type: string;
  value: string;
};
export type TSOPTopic = {
  id?: string;
  key?: string;
  name?: string;
  data?: TSOPTopicExample[];
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
};

const ruleSchema = yup
  .object({
    data: yup.object({
      id: yup.string(),
      key: yup.string(),
      name: yup.string(),
      data: yup.array().of(
        yup.object({
          type: yup.string(),
          value: yup.string(),
        })
      ),
    }),
  })
  .required();
export const TopicForm = ({ data, onOpenChange }: TTopicFormProps) => {
  const methods = useForm({
    resolver: yupResolver(ruleSchema),
    defaultValues: { data: { ...data } },
  });
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'data.data',
  });
  const onSubmit = async (data: any) => {
    window.alert(`Submitted data: ${JSON.stringify(data)}`);
  };
  return (
    <Popup
      open={!!data}
      onOpenChange={onOpenChange}
    >
      <PopupContent
        className="w-4/5 max-w-[700px] shadow-md"
        title={data?.id ? 'Edit Topic' : 'New Topic'}
      >
        <FormProvider {...methods}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col w-full px-4 pt-4 pb-6"
          >
            <div className="flex gap-4 items-center mb-4">
              <Field
                title="Name"
                className="w-full"
              >
                <Controller
                  name="data.name"
                  control={control}
                  rules={{ required: 'Name is required' }}
                  render={({ field }) => (
                    <EditableInput
                      {...field}
                      placeholder="Enter Rule Name"
                      value={field.value ?? 'New Topic'}
                    />
                  )}
                />
              </Field>
              <Field
                title="Key"
                className="w-full"
              >
                <Controller
                  name="data.key"
                  control={control}
                  rules={{ required: 'Key is required' }}
                  render={({ field }) => (
                    <EditableInput
                      {...field}
                      placeholder="Enter Rule Key"
                      value={field.value ?? 'TP-3'}
                    />
                  )}
                />
              </Field>
            </div>
            <div className="flex flex-col gap-2 mb-2">
              <div className="flex justify-between">
                <div className={cn(`text-body font-bold leading-[25.2px]`)}>
                  Samples ({`${fields.length}`})
                </div>
                <div className="relative inline-flex">
                  <Button
                    type="button"
                    size="s"
                    beforeIcon={
                      <Upload
                        size={16}
                        className="mr-2"
                      />
                    }
                  >
                    Upload
                    <input
                      type="file"
                      className="absolute opacity-0 size-full left-0 top-0 z-20 cursor-pointer"
                    />
                  </Button>
                </div>
              </div>
              <div className="flex flex-col gap-2 max-h-[300px] overflow-auto">
                {fields.map((field, index) => (
                  <Controller
                    name={`data.data[${index}]value`}
                    control={control}
                    key={field.id}
                    rules={{ required: 'data is required' }}
                    render={({ field }) => (
                      <div className="relative flex gap-x-3 group/textarea">
                        <textarea
                          {...field}
                          className="w-full h-[90px] border border-gray-300 rounded-md p-2 pr-8 focus:outline-none focus:border-primary-900 focus:shadow-field resize-none"
                          placeholder="Enter sample for topics"
                          value={field.value}
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-3 hover:text-primary-600 transition-all opacity-0 group-hover/textarea:opacity-100"
                          onClick={() => remove(index)}
                        >
                          <Trash2 size={20} />
                        </button>
                      </div>
                    )}
                  />
                ))}
              </div>
              <button
                type="button"
                className="w-full flex items-center gap-x-1 transition-all hover:p-2 rounded-md opacity-40 border-gray-300 hover:opacity-100 hover:border"
                onClick={() => {
                  append({
                    type: 'text',
                    value: '',
                  });
                }}
              >
                <div className="text-black hover:text-primary flex justify-center items-center">
                  <Plus size={20} />
                </div>
                New Step
              </button>
            </div>
            <div className="flex mt-4">
              <Button type="submit">Submit</Button>
            </div>
          </form>
        </FormProvider>
      </PopupContent>
    </Popup>
  );
};

export default TopicForm;
