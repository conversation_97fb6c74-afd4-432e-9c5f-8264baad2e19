import React, { memo } from 'react';
import { useField<PERSON><PERSON><PERSON>, Controller, useFormContext } from 'react-hook-form';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { ChevronDown, Move, Plus, Trash2 } from 'lucide-react';
import SOPStep from '../SOPStep';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';
import { Reorder, useDragControls } from 'framer-motion';
import EditableInput from '../EditableInput';
import { useQM } from '@cdss-modules/design-system';
import _ from 'lodash';

const SOPCategory = ({
  control,
  errors,
  index,
  remove,
  expanded,
  setExpanded,
  field,
  orderingMode,
}: any) => {
  const {
    fields: stepFields,
    append: appendStep,
    remove: removeStep,
  } = useFieldArray({
    control,
    name: `categories[${index}].steps`,
  });
  const dragControls = useDragControls();
  const { watch } = useFormContext();

  const [orderMatters, setOrderMatters] = React.useState(true);
  const { openedAdminSOPStep } = useQM();
  const isActiveStepNotEmpty = !_.isEmpty(watch(openedAdminSOPStep?.path));
  const isActive =
    openedAdminSOPStep?.path?.indexOf(`categories[${index}]`) > -1 &&
    isActiveStepNotEmpty;

  return (
    <Reorder.Item
      key={field.id}
      value={field}
      dragListener={false}
      dragControls={dragControls}
      className={cn(
        'w-full border p-3 rounded flex flex-col gap-y-4 transition-none',
        !expanded && 'hover:shadow-md',
        !expanded && isActive && 'bg-primary-200 shadow-md'
      )}
    >
      <div className="relative flex justify-between items-center gap-x-2 z-10 pointer-events-none">
        <div
          className="pointer-events-auto cursor-grab"
          onPointerDown={(e) => {
            dragControls.start(e);
          }}
        >
          <Move />
        </div>
        <Field
          title=""
          status={errors?.categories?.[index]?.name ? 'danger' : undefined}
          message={errors?.categories?.[index]?.name?.message}
          className="w-full"
        >
          <Controller
            name={`categories[${index}].name`}
            control={control}
            rules={{ required: 'Category name is required' }}
            render={({ field }) => (
              <>
                <EditableInput
                  {...field}
                  placeholder="Enter Stage Name"
                />
              </>
            )}
          />
        </Field>
        <div className="flex items-center gap-x-4 pointer-events-auto">
          <button
            type="button"
            onClick={() => remove(index)}
          >
            <Trash2 size={20} />
          </button>
          <button
            type="button"
            onClick={() => setExpanded(!expanded)}
            className={cn(
              expanded && 'rotate-180 cursor-pointer pointer-events-auto'
            )}
          >
            <ChevronDown />
          </button>
        </div>
      </div>
      {!expanded && (
        <button
          type="button"
          onClick={() => setExpanded(!expanded)}
          className={cn(
            'absolute left-0 top-0 w-full h-full z-0 cursor-pointer'
          )}
        ></button>
      )}
      <div className={cn('transition-opacity', !expanded && 'hidden')}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-x-2 mt-2 mb-2 hidden">
            <h4 className="text-body font-bold">Steps</h4>
          </div>

          <div className="flex items-center gap-2 hidden">
            <label
              className={`${orderMatters ? 'text-status-success' : 'text-grey-500'}`}
            >
              {orderMatters ? 'Validate by orders' : 'Validate without orders'}
            </label>
            <Switch
              size="s"
              activeColor="green"
              // defaultChecked={orderMatters}
              checked={orderMatters}
              onChange={() => setOrderMatters(!orderMatters)}
            />
          </div>
        </div>
        <div className="flex flex-col gap-y-3">
          {stepFields.map((step, stepIndex) => (
            <div key={step.id}>
              <SOPStep
                control={control}
                errors={errors}
                path={`categories[${index}].steps[${stepIndex}]`}
                stepIndex={stepIndex}
                removeStep={removeStep}
              />
            </div>
          ))}
          <button
            type="button"
            className="w-full flex items-center gap-x-1 transition-all hover:p-3 rounded-md opacity-40 hover:opacity-100 hover:bg-gray-100"
            onClick={() => {
              appendStep({
                name: `Step ${stepFields.length + 1}`,
                criteria: [],
                verificationRules: [],
                passingRules: [],
              });
            }}
          >
            <div className="text-black hover:text-primary flex justify-center items-center">
              <Plus size={20} />
            </div>
            New Step
          </button>
        </div>
      </div>
    </Reorder.Item>
  );
};

export default memo(SOPCategory);
