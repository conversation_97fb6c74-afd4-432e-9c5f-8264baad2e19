import React, { memo } from 'react';
import { Button } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Check, Edit, X } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';

export const EditableInput = ({ ...props }: any) => {
  const [editing, setEditing] = React.useState(false);
  if (editing) {
    return (
      <div className="relative flex items-center justify-between max-w-[500px] pointer-events-auto">
        <Input
          size="s"
          {...props}
        />
        <div
          className={cn(
            'inline-flex gap-x-2 px-2'
            // 'absolute right-0 top-1/2 -translate-y-1/2'
          )}
        >
          <Button
            type="button"
            variant="primary"
            asSquare
            className=""
            onClick={() => setEditing(false)}
          >
            <Check />
          </Button>
          <Button
            type="button"
            variant="secondary"
            asSquare
            className=""
            onClick={() => setEditing(false)}
          >
            <X />
          </Button>
        </div>
      </div>
    );
  }
  return (
    <button
      type="button"
      className="group/editable-input inline-flex items-center gap-x-2  pointer-events-auto"
      onClick={() => setEditing(true)}
    >
      <div className="inline-flex text-body font-bold">{props.value}</div>
      <Edit
        className=" group-hover/editable-input:text-primary"
        size={20}
      />
    </button>
  );
};

export default memo(EditableInput);
