import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { Reorder } from 'framer-motion';

import {
  Controller,
  useFieldArray,
  useForm,
  FormProvider,
} from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { ArrowBigDown, Edit, ListCollapse, Plus, Save } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {
  Button,
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  useQM,
} from '@cdss-modules/design-system';
import SOPCategory from '../SOPCategory';
import React, { Fragment, useEffect } from 'react';
import EditableInput from '../EditableInput';
import SOPStepDetails from '../SOPStepDetails';
import _ from 'lodash';
import EditableParticipants from '../EditableParticipants';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import dayjs from 'dayjs';
import { GLOBAL_DATE_FORMAT } from '@cdss-modules/design-system/lib/constants';
import EditableDateRange from '../EditableDateRange';

export type TSOPCriteria = {
  type: 'step-result' | 'metadata' | 'dict';
  key: string;
  relation?: 'exists' | 'equals';
  value: string;
};
export type TSOPVerificationRule = {
  dataType: 'text' | 'metadata' | 'dict' | 'rule-set';
  dataKey?: string;
  dataValue: string;
  relation?: 'exists' | 'equals' | 'all' | 'numberof';
  relationFactor: number;
  ruleSet?: string[];
};

export type TSOPExtractios = {
  type: 'metadata' | 'dict';
  key: string;
};

export type TSOPPassingRule = {
  type: 'all-rules-passed' | 'number of rules passed';
  numberOf?: number;
  rules?: string[];
};

export type TSOPStep = {
  name: string;
  participants: {
    [participantId: string]: {
      criteria: TSOPCriteria[];
      criteriaPassingRules: TSOPPassingRule;
      verificationRules: TSOPVerificationRule[];
      passingRules: TSOPPassingRule;
      extractions: TSOPExtractios[];
    };
  };
};
export type TSOPCategories = {
  id: string;
  name: string;
  steps: TSOPStep[];
};

export type TSOP = {
  id: string;
  categories: [];
  name: string;
  description: string;
  participants: {
    id?: string;
    name: string;
  }[];
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
};

const sopSchema = yup
  .object({
    id: yup.string().required(),
    participants: yup.array().of(
      yup.object({
        id: yup.string(),
        name: yup.string().required(),
      })
    ),
    categories: yup.array().of(
      yup.object({
        id: yup.string().required(),
        name: yup.string().required(),
        steps: yup.array().of(
          yup.object({
            name: yup.string().required(),
            participants: yup.lazy((value) => {
              if (!_.isEmpty(value)) {
                const validationObject = {
                  criteria: yup.array().of(
                    yup.object({
                      type: yup.string().required(),
                      key: yup.string().required(),
                      relation: yup.string(),
                      value: yup.string().required(),
                    })
                  ),
                  verificationRules: yup.array().of(
                    yup.object({
                      dataType: yup.string().required(),
                      dataKey: yup.string(),
                      dataValue: yup.string().required(),
                      relation: yup.string().required(),
                      relationFactor: yup.number().required(),
                    })
                  ),
                  passingRules: yup.array().of(
                    yup.object({
                      type: yup.string().required(),
                      numberOf: yup.number(),
                      rules: yup.array().of(yup.string()),
                    })
                  ),
                };
                const newEntries = Object.keys(value).reduce(
                  (acc, val) => ({
                    ...acc,
                    [val]: yup.object(validationObject),
                  }),
                  {}
                );

                return yup.object().shape(newEntries);
              }
              return yup.mixed().notRequired();
            }),
          })
        ),
      })
    ),
    name: yup.string().required(),
    description: yup.string(),
    createdBy: yup.string(),
    createdAt: yup.string(),
    updatedBy: yup.string(),
    updatedAt: yup.string(),
  })
  .required();

export type TSOPFormProps = {
  data?: TSOP;
  onOpenChange: (open: boolean) => void;
};

export const SOPForm = ({ data, onOpenChange }: TSOPFormProps) => {
  const { openedAdminSOPStep, updateOpenedAdminSOPStep } = useQM();
  const methods = useForm({
    resolver: yupResolver(sopSchema),
    defaultValues: {
      name: 'New SOP',
      participants: [
        {
          id: 'agent',
          name: 'Agent',
        },
        {
          id: 'client',
          name: 'Client',
        },
      ],
      categories: [
        {
          id: 'category-1',
          name: `New Stage 1`,
          steps: [
            {
              name: 'Step 1',
              participants: {
                agent: {
                  criteria: [],
                  verificationRules: [],
                  passingRules: [],
                },
                client: {
                  criteria: [],
                  verificationRules: [],
                  passingRules: [],
                },
              },
            },
          ],
        },
      ],
    },
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
  } = methods;

  const { fields, append, remove } = useFieldArray({
    control, // control props comes from useForm (optional: if you are using FormProvider)
    name: 'categories', // unique name for your Field Array
  });

  const onSubmit = async (data: any) => {
    window.alert(`Submitted data: ${JSON.stringify(data)}`);
  };

  useEffect(() => {
    if (data) {
      methods.reset(data);
    }
  }, [data, methods]);

  const [expandedCats, setExpandedCats] = React.useState<string[]>([]);
  const [orderingMode, setOrderingMode] = React.useState(false);

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col w-full h-full px-2 pt-0 pb-6"
      >
        <div className="flex-1 h-0 flex flex-col gap-y-4 overflow-auto">
          <div className="flex flex-col gap-x-4 w-full h-0 flex-1">
            <Field
              title={
                <div className="w-full relative flex items-center gap-x-2 justify-between py-2 z-30">
                  <div className="w-full flex items-center gap-x-8">
                    <div className="flex items-center gap-x-2">
                      <button
                        type="button"
                        onClick={() => onOpenChange(false)}
                        className="inline-flex gap-x-2 items-center group/sop-back hover:text-primary"
                      >
                        <Icon
                          name="back"
                          className="hidden group-hover/sop-back:inline-flex"
                        />
                        SOP
                      </button>
                      <span className="text-black">/</span>
                      <div className="w-full">
                        <Field
                          title={''}
                          icon={<Icon name="error" />}
                          status={errors?.name?.message ? 'danger' : undefined}
                          message={errors?.name?.message}
                        >
                          <Controller
                            name="name"
                            control={control}
                            rules={{ required: true }}
                            render={({ field }) => (
                              <EditableInput
                                placeholder="Enter SOP Name"
                                {...field}
                              />
                            )}
                          />
                        </Field>
                      </div>
                    </div>
                    <EditableParticipants
                      control={control}
                      errors={errors}
                    />
                    <EditableDateRange />
                  </div>
                  <div className="flex items-center gap-x-2">
                    <div className="flex items-center gap-x-2">
                      <Select
                        value={'v2024-sep'}
                        labelClassName="h-full text-remark"
                        labelContainerClassName="h-8"
                        onChange={(e) => setOrderingMode(e.target.value)}
                        options={[
                          { label: 'v2024-Sep (Latest Version)', value: 'v2024-sep', id: 'v2024-sep' },
                          { label: 'v2024-Jul', value: 'v2024-juk', id: 'v2024-jul' },
                          { label: 'v2024-Jan-2', value: 'v2024-j2', id: 'v2024-j2' },
                          { label: 'v2024-Jan-1', value: 'v2024-j1', id: 'v2024-j1' },
                        ]}
                      />
                    </div>
                    <Button
                      beforeIcon={<Save />}
                      size="s"
                      type="submit"
                    >
                      <span className="whitespace-nowrap">Save SOP</span>
                    </Button>
                  </div>
                </div>
              }
              titleClassName="mb-1"
              icon={<Icon name="error" />}
              status={errors?.categories?.message ? 'danger' : undefined}
              message={errors?.categories?.message}
              className="w-full flex flex-col flex-1 h-0"
              childrenContainerClassName="flex flex-col flex-1 h-0"
            >
              <ResizablePanelGroup
                direction="horizontal"
                className="relative flex-1 !h-0 gap-x-0"
              >
                <ResizablePanel>
                  <Reorder.Group
                    values={fields}
                    axis="y"
                    onReorder={(v) => setValue('categories', v)}
                    className="h-full"
                  >
                    <div
                      className={cn(
                        'flex flex-col gap-y-3 pr-3 h-full flex-1 overflow-y-auto scrollbar-hide'
                        // hasMultipleValues && 'pr-8'
                      )}
                    >
                      {fields.map((field, index) => (
                        <Fragment key={field.id}>
                          <div className="relative flex gap-x-4">
                            <SOPCategory
                              control={control}
                              errors={errors}
                              index={index}
                              remove={remove}
                              expanded={
                                orderingMode
                                  ? false
                                  : expandedCats.includes(field.id)
                              }
                              setExpanded={() => {
                                setExpandedCats((prev) => {
                                  if (prev.includes(field.id)) {
                                    return prev.filter((id) => id !== field.id);
                                  }
                                  return [...prev, field.id];
                                });
                              }}
                              field={field}
                              orderingMode={orderingMode}
                            />
                          </div>
                          {index !== fields.length - 1 && (
                            <div className="flex justify-center items-center hidden">
                              <ArrowBigDown
                                size={40}
                                className="text-primary-200"
                              />
                            </div>
                          )}
                        </Fragment>
                      ))}
                      <button
                        type="button"
                        className="w-full flex items-center gap-x-1 transition-all hover:p-3 rounded-md opacity-40 hover:opacity-100 hover:border hover:border-grey-200"
                        onClick={() => {
                          append({
                            id: `category-${Math.random()}`,
                            name: `New Stage ${fields.length + 1}`,
                            steps: [
                              {
                                name: 'Step 1',
                                participants: {
                                  agent: {
                                    criteria: [],
                                    verificationRules: [],
                                    passingRules: [],
                                  },
                                  client: {
                                    criteria: [],
                                    verificationRules: [],
                                    passingRules: [],
                                  },
                                },
                              },
                            ],
                          });
                        }}
                      >
                        <div className="text-black hover:text-primary flex justify-center items-center">
                          <Plus size={20} />
                        </div>
                        New Stage
                      </button>
                    </div>
                  </Reorder.Group>
                </ResizablePanel>
                <ResizableHandle className="w-0 hover:w-1" />
                <ResizablePanel>
                  <div className="flex flex-col gap-y-4 h-full bg-gray-100 rounded-md">
                    {openedAdminSOPStep &&
                      !_.isEmpty(watch(openedAdminSOPStep?.path)) ? (
                      <>
                        <SOPStepDetails {...openedAdminSOPStep} />
                      </>
                    ) : (
                      <div className="size-full p-3 flex flex-col gap-2 items-center justify-center text-t6 text-grey-400">
                        <ListCollapse size={60} />
                        Select a step to view details
                      </div>
                    )}
                  </div>
                </ResizablePanel>
              </ResizablePanelGroup>
            </Field>
          </div>
          <div className="flex justify-between gap-y-4 pt-4 h-10 overflow-auto hidden">
            {JSON.stringify(watch())}
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default SOPForm;
