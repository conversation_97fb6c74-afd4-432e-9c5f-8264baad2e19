import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { useState } from 'react';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import { SortingButton, Tooltip } from '@cdss-modules/design-system';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { Plus } from 'lucide-react';
import DictionaryActionMenu from '../DictionaryActionMenu';
import SOPForm from '../SOPForm';
import { DUMMY_SOP } from '../../dummy';

// Types
type TSopData = {
  id?: string;
  name?: string;
  latestVersion?: string;
  sop?: any;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
};

type TSopDataColumn = keyof TSopData;

const DICTIONARY_COLUMN_LABEL: any = {
  id: 'ID',
  name: 'Name',
  'sop.name': 'Name',
  latestVersion: 'Latest Version',
  createdBy: 'Created By',
  createdAt: 'Created At',
  updatedBy: 'Updated By',
  updatedAt: 'Updated At',
};

const generateColumns = (
  columns: TSopDataColumn[],
  columnOrdering: string[],
  sortOrder: any,
  setSortOrder: (input: any) => void,
  openEntity: (id?: string) => void
) => {
  const selectionCol = {
    id: 'select',
    header: ({ table }: any) => (
      <div
        className="bg-white z-30 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            table.toggleAllPageRowsSelected(isSelected);
          }}
        />
      </div>
    ),
    cell: ({ row }: any) => (
      <div
        className="z-0 inline-flex items-center my-1"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          checked={row.getIsSelected()}
          onChange={(e: any) => {
            const isSelected = e?.target.checked;
            row.toggleSelected(isSelected);
          }}
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const actionCol = {
    id: 'action',
    header: () => (
      <div>
        <Tooltip
          trigger={
            <button
              type="button"
              className="bg-primary text-white rounded-full size-6 flex-none flex justify-center items-center animate-pulse hover:animate-none"
              onClick={() => {
                openEntity();
              }}
            >
              <Plus size={18} />
            </button>
          }
          content="Add New Entity"
        />
      </div>
    ),
    cell: (
      <div className="flex gap-x-2 z-0">
        <DictionaryActionMenu data={{}} />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const orderedColumns = columnOrdering
    ? columns.sort((firstEl, secondEl) => {
        const firstIndex = columnOrdering?.findIndex(
          (item: any) => item.name === firstEl
        );
        const secondIndex = columnOrdering?.findIndex(
          (item: any) => item.name === secondEl
        );
        return firstIndex - secondIndex;
      })
    : columns;

  const formattedColumns = orderedColumns.map((column: string) => {
    const isDate = column === 'createdAt' || column === 'updatedAt';

    return {
      id: column,
      accessorKey: column,
      header: () => {
        return (
          <SortingButton
            sorting={
              sortOrder?.[column]
                ? sortOrder?.[column] === 'ASC'
                  ? 'asc'
                  : 'desc'
                : false
            }
            onClick={async () => {
              const targetSortOrder =
                sortOrder?.[column] === 'ASC' ? 'DESC' : 'ASC';
              setSortOrder({
                [column]: targetSortOrder,
              });
            }}
          >
            {DICTIONARY_COLUMN_LABEL?.[column] || column}
          </SortingButton>
        );
      },
      cell: ({ row }) => {
        let val = row.getValue(column) as any;
        if (isDate) val = dayjs(val).format(GLOBAL_DATETIME_FORMAT);
        if (column === 'values') val = val?.map((v: any) => v.value).join(', ');
        return <div>{val}</div>;
      },
    } as ColumnDef<TSopData>;
  });

  return [selectionCol, ...formattedColumns, actionCol];
};

export const SOP = () => {
  const dummySop = DUMMY_SOP as TSopData[];
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TSopData>>();
  const [sortOrder, setSortOrder] = useState<any>();
  const [openedEntity, setOpenedEntity] = useState<TSopData | null>(null);
  //   const [shownColumns, setShownColumns] = useState<TSopDataColumn[]>([]);
  const shownColumns = [
    'sop.name',
    'latestVersion',
    'createdBy',
    'createdAt',
    'updatedBy',
    'updatedAt',
  ] as TSopDataColumn[];
  console.log('openedEntity', openedEntity);

  return (
    <div className="px-6 pt-1 pb-6 flex flex-col h-full gap-y-4 overflow-auto">
      {openedEntity ? (
        <SOPForm
          data={openedEntity?.sop}
          onOpenChange={(open: boolean) => {
            if (!open) setOpenedEntity(null);
          }}
        />
      ) : (
        <div className="flex-1 h-0">
          <DataTable<TSopData>
            data={dummySop}
            columns={
              generateColumns(
                shownColumns,
                [],
                sortOrder,
                (input) => {
                  setSortOrder(input);
                },
                (id?: string) => {
                  setOpenedEntity(
                    dummySop?.find((d) => d.id === `${id}`) || {}
                  );
                }
              ) as any
            }
            loading={false}
            // error={error?.message}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onClickRow={(row) => {
              setOpenedEntity(dummySop?.[row?.index || 0] || {});
            }}
            onTableSetUp={(table) => setTable(table)}
          />
        </div>
      )}
    </div>
  );
};

export default SOP;
