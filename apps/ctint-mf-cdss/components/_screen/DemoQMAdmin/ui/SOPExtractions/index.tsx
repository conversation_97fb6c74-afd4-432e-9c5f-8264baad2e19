import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Trash2 } from 'lucide-react';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { DUMMY_DICTIONARY, DUMMY_META } from '../../dummy';
const criteriaTypeOptions = [
  { label: 'Metadata', value: 'metadata' },
  { label: 'Dictionary', value: 'dict' },
];

const metaOptions = DUMMY_META.map((meta) => ({
  label: meta.name,
  value: meta.metaKey,
  id: meta.id,
}));

const dictionaryOptions = DUMMY_DICTIONARY.map((dict) => ({
  label: dict.name,
  value: dict.entityKey,
  id: dict.id,
}));

const SOPExtractions = ({ control, path, index, remove }: any) => {
  const { watch } = useFormContext();
  const dataType = watch(`${path}.type`);
  return (
    <div className="flex flex-col gap-2 mb-4 w-full">
      <div className="w-full flex items-center gap-4 just">
        <div className="flex gap-4 items-center">
          <div>{`E${index + 1}.`}</div>
          <Field
            title=""
            className="flex-1"
          >
            <Controller
              name={`${path}.type`}
              control={control}
              rules={{ required: 'Type is required' }}
              render={({ field }) => (
                <Select
                  {...field}
                  options={criteriaTypeOptions?.map((option) => ({
                    id: option.value,
                    label: option.label,
                    value: option.value,
                  }))}
                  placeholder="Select Type"
                />
              )}
            />
          </Field>
          {dataType === 'metadata' && (
            <>
              <div>:</div>
              {dataType === 'metadata' && (
                <Field title="">
                  <Controller
                    name={`${path}.key`}
                    control={control}
                    rules={{ required: 'Metadata is required' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={metaOptions}
                        placeholder="Metadata"
                      />
                    )}
                  />
                </Field>
              )}
              {dataType === 'dict' && (
                <Field title="">
                  <Controller
                    name={`${path}.key`}
                    control={control}
                    rules={{ required: 'Dictionary is required' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={dictionaryOptions}
                        placeholder="Dictionary"
                      />
                    )}
                  />
                </Field>
              )}
            </>
          )}
        </div>
        <button
          type="button"
          onClick={() => remove(index)}
        >
          <Trash2 size={20} />
        </button>
      </div>
    </div>
  );
};

export default SOPExtractions;
