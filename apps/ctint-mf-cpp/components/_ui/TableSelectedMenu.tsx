'use client';
import React, { useMemo, useState } from 'react';

import Button from '@cdss-modules/design-system/components/_ui/Button';
import <PERSON>th<PERSON><PERSON><PERSON> from '@cdss-modules/design-system/components/_ui/AuthChecker';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';

import DropdownBox from '@cdss-modules/design-system/components/_ui/DropdownBox';

import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';

type TTableSelectedMenuProps = {
  data: any;
  rowSelection: any;
  show?: boolean;
  onClear: () => void;
  onClearOne: (id: number | string) => void;
  isDownloading?: boolean;
  onDownload: () => void;
  onAssign: (data: any[]) => void;
  popupAlign?: 'start' | 'center' | 'end';
};

const TableSelectedMenu = ({
  data,
  rowSelection,
  show,
  onClear,
  onClearOne,
  onDownload,
  onAssign,
  isDownloading,
  popupAlign = 'center',
}: TTableSelectedMenuProps) => {
  const { t } = useTranslation();
  const [dropdownBoxOpen, setDropdownBoxOpen] = useState(false);

  const handleDropdownBoxOpen = () => {
    setDropdownBoxOpen(!dropdownBoxOpen);
  };

  const mutatedArray = Object.entries(rowSelection).map(([key, value]) => ({
    id: [key][0],
    selected: value,
  }));

  const selectedRow = () => {
    const result = mutatedArray?.map((obj) => {
      const key = Number(obj.id);

      const label = data?.[key]?.id;

      return (
        <div
          key={`download-items-${label}`}
          className="flex items-center gap-4"
          title={label}
        >
          <button
            key={obj.id}
            onClick={() => onClearOne(key)}
          >
            <Icon name="cross" />
          </button>
          <p className="truncate">{label}</p>
        </div>
      );
    });
    return result;
  };
  const rowSelectionCount = useMemo(() => {
    const result = Object.keys(rowSelection).map((n) => n);

    return result;
  }, [rowSelection]);
  if (!show) return null;

  return (
    <AuthChecker
      // requiredPemissions={{
      //   global: {
      //     portals: ['ctint-mf-cpp'],
      //   },
      //   user: {
      //     permissions: ['ctint-mf-cpp.recording.download'],
      //   },
      // }}
      emptyWhenUnauthorized
    >
      <DropdownBox
        open={dropdownBoxOpen}
        onOpenChange={handleDropdownBoxOpen}
        trigger={
          <button className="flex items-center gap-2 p-[2px] bg-white border border-black text-black rounded-[4px]">
            <span className="flex justify-center">
              <Checkbox checked={rowSelectionCount.length > 0} />
            </span>
            <span className="flex justify-center mr-2">
              {`+${rowSelectionCount.length}`}
            </span>
          </button>
        }
        align={popupAlign}
      >
        <div className="w-[400px] rounded-2xl flex flex-col shadow-card overflow-hidden">
          <div className="bg-primary-500 rounded-tl-2xl rounded-tr-2xl flex justify-between px-4 py-2 ">
            <div className="text-black font-bold">
              {t('ctint-mf-cpp.filter.selectedList')} (
              {rowSelectionCount?.length})
            </div>
            <button onClick={handleDropdownBoxOpen}>
              <Icon name="cross" />
            </button>
          </div>
          <div className="flex flex-col overflow-auto px-4 py-6 gap-y-6">
            {selectedRow()}
          </div>

          <div className="flex items-center justify-between px-4 py-2 w-full border-t border-black">
            <Button
              variant="blank"
              onClick={() => onClear()}
              disabled={isDownloading}
            >
              {t('ctint-mf-cpp.filter.clear')}
            </Button>
            <div className="inline-flex gap-x-2">
              <Button
                variant="primary"
                onClick={() => {
                  onAssign(
                    mutatedArray.map((item) => {
                      const key = Number(item.id);
                      return data?.[key]?.id || '';
                    })
                  );
                }}
                disabled={isDownloading}
              >
                Assign Evaluation
              </Button>
              <Button
                variant="primary"
                onClick={() => onDownload()}
                disabled={isDownloading}
              >
                {isDownloading
                  ? `${t('ctint-mf-cpp.filter.prepare')}...`
                  : t('ctint-mf-cpp.filter.download')}
              </Button>
            </div>
          </div>
        </div>
      </DropdownBox>
    </AuthChecker>
  );
};

export default TableSelectedMenu;
