/* eslint-disable react/no-unescaped-entities */
import React, { useMemo } from 'react';
import { useQM } from '@cdss-modules/design-system';
import TimeDurationSetter from '@cdss-modules/design-system/components/_ui/TimeDurationSetter';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import { ClipboardChe<PERSON>, Frown, Smile } from 'lucide-react';
import { DUMMY_CS_EVALUATION_FORM } from '../../../lib/dummy/qa';
import { getQALiveStat } from '../../../lib/qa';
import { cn, secondsToFormat } from '@cdss-modules/design-system/lib/utils';

export const QMSOPStages = () => {
  const {
    activeQMSection,
    openedQA,
    qaFormAnswers,
    updateQMActiveSection,
    scrollToScriptClickHandler,
    scrollToStageClickHandler,
  } = useQM();

  const qaLiveStat = useMemo(() => {
    const form = DUMMY_CS_EVALUATION_FORM;
    const answers = qaFormAnswers?.[`${openedQA}`] || {};
    if (openedQA && form && answers) {
      return getQALiveStat(form, answers);
    }
    return null;
  }, [openedQA, qaFormAnswers]);

  const { seek } = useGlobalAudioPlayer();
  const hidden = true;

  if (!openedQA) return null;
  if (hidden) return null;

  return (
    <div className="border-b border-grey-200 mt-4">
      <div className="relative w-full overflow-auto scrollbar-hide">
        <div className="flex items-stretch h-full overflow-y-hidden">
          {DUMMY_CS_EVALUATION_FORM.questions?.map((sec, si) => {
            const sid = `${si + 1}`;
            const skey = `sec-${sid}`;
            const isActive = activeQMSection === sec.id;
            const secLiveInfo = qaLiveStat?.infoBySections?.[sec?.id || ''];
            const secTotalQuestions = secLiveInfo?.totalQuestions || 0;
            const secTotalQuestionsAnswered =
              secLiveInfo?.totalQuestionsAnswered || 0;
            const answers = qaLiveStat?.answers?.[sec?.id || ''];
            const secStart = answers?.duration?.start;
            const secEnd = answers?.duration?.end;
            const secStartDisplay = secondsToFormat(secStart);
            const secEndDisplay = secondsToFormat(secEnd);
            return (
              <button
                key={skey}
                onClick={() => {
                  updateQMActiveSection(sec.id);
                  scrollToScriptClickHandler(`script-${sec.id}`);
                  scrollToStageClickHandler(`stage-${sec.id}`);
                }}
                className={cn(
                  'relative w-full py-2 pl-14 pr-0 first:pl-4 last:pr-4 group hover:bg-primary-300 transition-all',
                  isActive ? 'bg-primary-300' : 'bg-primary-100'
                )}
              >
                <div
                  className={cn(
                    'relative font-bold whitespace-nowrap z-20 text-left'
                  )}
                >
                  <div>
                    {sid}. {sec.title || sec.section}
                  </div>
                  {secTotalQuestions > 0 && (
                    <div className="flex items-center gap-x-1 text-footnote">
                      (
                      <div className="flex items-center gap-x-2">
                        {answers?.duration ? (
                          <button
                            onClick={() => seek(secStart)}
                            className="block text-left text-footnote text-status-info underline group"
                          >
                            <span className="group-hover:text-primary-900 group-hover:underline">
                              {secStartDisplay} - {secEndDisplay}
                            </span>
                          </button>
                        ) : (
                          <TimeDurationSetter
                            updateStartTime={(time) => null}
                            updateEndTime={(time) => null}
                            startTime={0}
                            endTime={0}
                          />
                        )}
                        <span className="flex items-center gap-x-1">
                          <ClipboardCheck size={16} />
                          {secLiveInfo?.totalQuestionsAnswered}/
                          {secLiveInfo?.totalQuestions}
                        </span>
                        {secTotalQuestionsAnswered >= secTotalQuestions && (
                          <div className="flex items-center gap-x-1">
                            {secLiveInfo?.passed ? (
                              <>
                                <Smile
                                  className="text-status-success"
                                  size={16}
                                />
                                Passed
                              </>
                            ) : (
                              <>
                                <Frown
                                  className="text-status-danger"
                                  size={16}
                                />
                                Failed
                              </>
                            )}
                          </div>
                        )}
                      </div>
                      )
                    </div>
                  )}
                </div>
                <div
                  className={cn(
                    'absolute top-0 left-full z-10 aspect-square h-full border-t border-r border-grey-200 transition-all -translate-x-1/2 rotate-45 group-last:hidden group-hover:bg-primary-300',
                    isActive ? 'bg-primary-300' : 'bg-primary-100'
                  )}
                />
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default QMSOPStages;
