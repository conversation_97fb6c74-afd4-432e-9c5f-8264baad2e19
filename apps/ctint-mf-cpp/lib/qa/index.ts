export const getQALiveStat = (form: any, answers: any) => {
  if (form && answers) {
    let totalScore = 0;
    let criticalScore = 0;
    let totalQuestions = 0;
    let totalQuestionsAnswered = 0;
    let totalPoints = 0;
    let pointsGained = 0;
    let totalCriticalPoints = 0;
    let criticalPointsGained = 0;
    let hasFatalZeroPoint = false;
    const infoBySections: Record<
      string,
      {
        passed: boolean;
        totalQuestionsAnswered: number;
        totalQuestions: number;
      }
    > = {};

    const calculateQuestions = (section: any, sid?: any) => {
      if (section.subSections) {
        section.subSections.forEach((subSection: any) =>
          calculateQuestions(subSection, section.id)
        );
      } else {
        const sectionId = sid || section.id;
        if (!infoBySections[sectionId]) {
          infoBySections[sectionId] = {
            passed: true,
            totalQuestionsAnswered: 0,
            totalQuestions: 0,
          };
        }

        section?.questions?.forEach((question: any) => {
          if (question.questionType !== 'other') {
            totalQuestions += 1;
            infoBySections[sectionId].totalQuestions += 1;
          }

          const answersOfSections = sid
            ? answers?.[sid]?.[section.id]
            : answers?.[section.id];

          if (
            answersOfSections &&
            answersOfSections[question.id] &&
            question.questionType !== 'other'
          ) {
            totalQuestionsAnswered += 1;
            infoBySections[sectionId].totalQuestionsAnswered += 1;
            const selectedAnswerId = answersOfSections[question.id];
            const selectedAnswer = question.answers?.find(
              (a: any) => a.id === selectedAnswerId
            );

            if (
              question.questionType === 'fatal' &&
              selectedAnswer?.point === 0
            ) {
              hasFatalZeroPoint = true;
              infoBySections[sectionId].passed = false;
            }

            if (selectedAnswer) {
              pointsGained += selectedAnswer.point;

              if (
                question.questionType === 'critical' ||
                question.questionType === 'fatal'
              ) {
                criticalPointsGained += selectedAnswer.point;
              }
            }
          }

          if (
            question.questionType === 'critical' ||
            question.questionType === 'fatal'
          ) {
            totalCriticalPoints += question?.answers?.reduce(
              (sum: number, answer: any) => {
                const answerPt = answer?.point || 0;
                return answerPt > sum ? answerPt : sum;
              },
              0
            );
          }

          totalPoints +=
            question?.answers?.reduce((sum: number, answer: any) => {
              const answerPt = answer?.point || 0;
              return answerPt > sum ? answerPt : sum;
            }, 0) || 0;
        });
      }
    };

    form.questions.forEach((section: any) => {
      calculateQuestions(section);
    });

    if (hasFatalZeroPoint) {
      pointsGained = 0;
    }

    totalScore = totalPoints ? (pointsGained / totalPoints) * 100 : 0;
    criticalScore = totalCriticalPoints
      ? (criticalPointsGained / totalCriticalPoints) * 100
      : 0;

    return {
      totalQuestions,
      totalQuestionsAnswered,
      totalPoints,
      pointsGained,
      totalCriticalPoints,
      criticalPointsGained,
      totalScore: Math.round(totalScore),
      criticalScore: Math.round(criticalScore),
      hasFatalZeroPoint,
      infoBySections,
      answers,
    };
  }
};
