import { DUMMY_QM_FORM } from '@cdss-modules/design-system';

export const DUMMY_EVALUATION_FORMS = [
  {
    id: 'cse-1',
    label: 'Customer Service Evaluation Form',
    versions: [
      {
        id: 'cse-1-1',
        label: 'v2024-06-25',
      },
      {
        id: 'cse-1-2',
        label: 'v2024-04-15',
      },
    ],
    value: 'cse-1',
  },
  {
    id: 'se-1',
    label: 'Sales Evaluation Form',
    versions: [
      {
        id: 'se-1-1',
        label: 'v2024-03-01',
      },
    ],
    value: 'se-1',
  },
  {
    id: 'tse',
    label: 'Technical Support Evaluation Form',
    versions: [
      {
        id: 'tse-1-1',
        label: 'v2023-12-31',
      },
    ],
    value: 'tse',
  },
];

export const DUMMY_CS_EVALUATION_FORM = DUMMY_QM_FORM;

export const DUMMUY_QA_DATA = [
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000001',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-06-25',
    evaluator: 'CSQM Model (Auto QM)',
    isAuto: true,
    date: '2024-07-09T12:00:00Z',
    releasedAt: '2024-07-10T12:00:00Z',
    status: 'released',
  },
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000002',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-06-25',
    evaluator: 'CSQM Model (Auto QM)',
    isAuto: true,
    date: '2024-07-03T12:00:00Z',
    releasedAt: '2024-07-03T12:00:00Z',
    status: 'released',
  },
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000003',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-06-25',
    evaluator: 'Jane Doe',
    date: '2024-07-01T12:00:00Z',
    releasedAt: '2024-07-01T12:00:00Z',
    status: 'released',
  },
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000004',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-04-15',
    evaluator: 'Mary Poppins',
    date: '2024-07-01T12:00:00Z',
    status: 'evaluated',
  },
  {
    id: 'QADBMNLAM52MJ0GUEKVQ8I0ODO000005',
    form: 'Customer Service Evaluation Form',
    formVersion: 'v2024-06-25',
    evaluator: 'Admin',
    score: null,
    assignedAt: '2024-07-01T12:00:00Z',
    status: 'assigned',
  },
];

export const DUMMY_DEFAULT_ANSWERS = {
  QADBMNLAM52MJ0GUEKVQ8I0ODO000001: {
    s_1: {
      duration: {
        start: 0,
        end: 30,
      },
      ss_1_1: {
        q_1_1_1: '1_1_1_y',
        q_1_1_2: '1_1_2_n',
      },
    },
    s_2: {
      duration: {
        start: 20,
        end: 50,
      },
      ss_2_1: {
        q_2_1_1: '2_1_1_y',
        q_2_1_2: '2_1_2_y',
        q_2_1_3: '2_1_3_n',
      },
    },
    s_3: {
      duration: {
        start: 40,
        end: 60,
      },
      ss_3_1: {
        q_3_1_1: '3_1_1_y',
        q_3_1_2: '3_1_2_n',
      },
    },
    s_4: {
      duration: {
        start: 70,
        end: 90,
      },
      ss_4_1: {
        q_4_1_1: '4_1_1_y',
        q_4_1_2: '4_1_2_y',
        q_4_1_3: '4_1_3_y',
        q_4_1_4: '4_1_4_n',
      },
    },
    s_5: {
      duration: {
        start: 80,
        end: 110,
      },
      ss_5_1: {
        q_5_1_1: '5_1_1_y',
        q_5_1_2: '5_1_2_y',
      },
    },
    s_6: {
      duration: {
        start: 100,
        end: 120,
      },
      ss_6_1: {
        q_6_1_1: '6_1_1_y',
        q_6_1_2: '6_1_2_y',
      },
    },
    s_7: {
      duration: {
        start: 120,
        end: 150,
      },
      ss_7_1: {
        q_7_1_1: '7_1_1_y',
        q_7_1_2: '7_1_2_n',
        q_7_1_3: '7_1_3_y',
      },
    },
    s_8: {
      duration: {
        start: 120,
        end: 170,
      },
      ss_8_1: {
        q_8_1_1: '8_1_1_y',
        q_8_1_2: '8_1_2_y',
        q_8_1_3: '8_1_3_y',
        q_8_1_4: '8_1_4_n',
        q_8_1_5: '8_1_5_y',
      },
    },
    s_04: {
      ss_04_1: {
        q04_1_1: 'Polite but bad closing.',
      },
    },
    s_05: {
      ss_05_1: {
        q05_1_1: '05_1_1_2',
      },
    },
    tags: {
      q_1_1_1: {
        ai: true,
        accuracy: 0.92,
        target: 's_1',
        type: 'positive',
        time: 5,
        label: 'Q.1.1.1',
        desc: 'Recording started correctly',
      },
      q_1_1_2: {
        ai: true,
        accuracy: 0.12,
        target: 's_1',
        type: 'negative',
        time: 10,
        label: 'Q.1.1.2',
        desc: 'Agent Name not provided',
      },
      q_2_1_1: {
        ai: true,
        accuracy: 0.87,
        target: 's_2',
        type: 'positive',
        time: 25,
        label: 'Q.2.1.1',
        desc: 'Customer provided personal ID info',
      },
      q_2_1_2: {
        ai: true,
        accuracy: 0.91,
        target: 's_2',
        type: 'positive',
        time: 30,
        label: 'Q.2.1.2',
        desc: 'Customer provided personal ID info',
      },
      q_2_1_3: {
        ai: true,
        accuracy: 0.4,
        target: 's_2',
        type: 'negative',
        time: 32,
        label: 'Q.2.1.3',
        desc: 'Only provided address',
      },
      q_2_2_1: {
        ai: true,
        accuracy: 0.88,
        target: 's_2',
        type: 'positive',
        time: 35,
        label: 'Q.2.2.1',
        desc: "Confirmed customer's investment experience",
      },
      q_2_2_2: {
        ai: true,
        accuracy: 0.48,
        target: 's_2',
        type: 'negative',
        time: 40,
        label: 'Q.2.2.2',
        desc: 'Failed witness verification',
      },
      q_2_3_1: {
        ai: true,
        accuracy: 0.9,
        target: 's_2',
        type: 'positive',
        time: 45,
        label: 'Q.2.3.1',
        desc: 'Authorized person confirmed',
      },
      q_3_1_1: {
        ai: true,
        accuracy: 0.93,
        target: 's_3',
        type: 'positive',
        time: 50,
        label: 'Q.3.1.1',
        desc: 'Investment experience verified',
      },
      q_3_1_2: {
        ai: true,
        accuracy: 0.2,
        target: 's_3',
        type: 'negative',
        time: 55,
        label: 'Q.3.1.2',
        desc: "Not yet confirmed customer's non-vulnerable status",
      },
      q_4_1_1: {
        ai: true,
        accuracy: 0.91,
        target: 's_4',
        type: 'positive',
        time: 70,
        label: 'Q.4.1.1',
        desc: 'Witness name confirmed',
      },
      q_4_1_2: {
        ai: true,
        accuracy: 0.88,
        target: 's_4',
        type: 'positive',
        time: 75,
        label: 'Q.4.1.2',
        desc: 'Witness age confirmed',
      },
      q_4_1_3: {
        ai: true,
        accuracy: 0.95,
        target: 's_4',
        type: 'positive',
        time: 80,
        label: 'Q.4.1.3',
        desc: 'Witness education level confirmed',
      },
      q_4_1_4: {
        ai: true,
        accuracy: 0.43,
        target: 's_4',
        type: 'negative',
        time: 85,
        label: 'Q.4.1.4',
        desc: 'Failed to confirm witness understanding',
      },
      q_5_1_1: {
        ai: true,
        accuracy: 0.94,
        target: 's_5',
        type: 'positive',
        time: 90,
        label: 'Q.5.1.1',
        desc: "Authorized person's product knowledge confirmed",
      },
      q_5_1_2: {
        ai: true,
        accuracy: 0.89,
        target: 's_5',
        type: 'positive',
        time: 95,
        label: 'Q.5.1.2',
        desc: "Confirmed authorized person's understanding of derivatives",
      },
      q_6_1_1: {
        ai: true,
        accuracy: 0.92,
        target: 's_6',
        type: 'positive',
        time: 100,
        label: 'Q.6.1.1',
        desc: 'Customer confirmed as experienced in product category',
      },
      q_6_1_2: {
        ai: true,
        accuracy: 0.96,
        target: 's_6',
        type: 'positive',
        time: 105,
        label: 'Q.6.1.2',
        desc: "Confirmed customer's experience with similar investment products",
      },
      q_7_1_1: {
        ai: true,
        accuracy: 0.93,
        target: 's_7',
        type: 'positive',
        time: 110,
        label: 'Q.7.1.1',
        desc: 'Customer confirmed they had sufficient time to consider the investment',
      },
      q_7_1_2: {
        ai: true,
        accuracy: 0.47,
        target: 's_7',
        type: 'negative',
        time: 115,
        label: 'Q.7.1.2',
        desc: 'Customer did not understand product characteristics and risks',
      },
      q_7_1_3: {
        ai: true,
        accuracy: 0.91,
        target: 's_7',
        type: 'positive',
        time: 120,
        label: 'Q.7.1.3',
        desc: 'Customer accepted potential losses associated with the product',
      },
      q_8_1_1: {
        ai: true,
        accuracy: 0.88,
        target: 's_8',
        type: 'positive',
        time: 125,
        label: 'Q.8.1.1',
        desc: 'Customer agreed with the suitability assessment results',
      },
      q_8_1_2: {
        ai: true,
        accuracy: 0.95,
        target: 's_8',
        type: 'positive',
        time: 130,
        label: 'Q.8.1.2',
        desc: 'Confirmed investment amount and account details',
      },
      q_8_1_3: {
        ai: true,
        accuracy: 0.97,
        target: 's_8',
        type: 'positive',
        time: 135,
        label: 'Q.8.1.3',
        desc: 'Customer confirmed readiness to proceed with the transaction',
      },
      q_8_1_4: {
        ai: true,
        accuracy: 0.52,
        target: 's_8',
        type: 'negative',
        time: 140,
        label: 'Q.8.1.4',
        desc: 'Customer did not understand the irrevocability of the application once accepted',
      },
      q_8_1_5: {
        ai: true,
        accuracy: 0.91,
        target: 's_8',
        type: 'positive',
        time: 145,
        label: 'Q.8.1.5',
        desc: 'Confirmed that customer was informed of product issuance results',
      },
    },
  },
  QADBMNLAM52MJ0GUEKVQ8I0ODO000002: {
    s_1: {
      duration: {
        start: 0,
        end: 30,
      },
      ss_1_1: {
        q_1_1_1: '1_1_1_y',
        q_1_1_2: '1_1_2_y',
      },
    },
    s_2: {
      duration: {
        start: 20,
        end: 50,
      },
      ss_2_1: {
        q_2_1_1: '2_1_1_y',
        q_2_1_2: '2_1_2_y',
        q_2_1_3: '2_1_3_y',
      },
    },
    s_3: {
      duration: {
        start: 40,
        end: 60,
      },
      ss_3_1: {
        q_3_1_1: '3_1_1_y',
        q_3_1_2: '3_1_2_y',
      },
    },
    s_4: {
      duration: {
        start: 70,
        end: 90,
      },
      ss_4_1: {
        q_4_1_1: '4_1_1_y',
        q_4_1_2: '4_1_2_y',
        q_4_1_3: '4_1_3_y',
        q_4_1_4: '4_1_4_y',
      },
    },
    s_5: {
      duration: {
        start: 80,
        end: 110,
      },
      ss_5_1: {
        q_5_1_1: '5_1_1_y',
        q_5_1_2: '5_1_2_y',
      },
    },
    s_6: {
      duration: {
        start: 100,
        end: 120,
      },
      ss_6_1: {
        q_6_1_1: '6_1_1_y',
        q_6_1_2: '6_1_2_y',
      },
    },
    s_7: {
      duration: {
        start: 120,
        end: 150,
      },
      ss_7_1: {
        q_7_1_1: '7_1_1_y',
        q_7_1_2: '7_1_2_y',
        q_7_1_3: '7_1_3_y',
      },
    },
    s_8: {
      duration: {
        start: 120,
        end: 170,
      },
      ss_8_1: {
        q_8_1_1: '8_1_1_y',
        q_8_1_2: '8_1_2_y',
        q_8_1_3: '8_1_3_y',
        q_8_1_4: '8_1_4_y',
        q_8_1_5: '8_1_5_y',
      },
    },
    s_04: {
      ss_04_1: {
        q04_1_1: 'Polite but bad closing.',
      },
    },
    s_05: {
      ss_05_1: {
        q05_1_1: '05_1_1_2',
      },
    },
    tags: {
      q_1_1_1: {
        ai: true,
        accuracy: 0.92,
        target: 's_1',
        type: 'positive',
        time: 5,
        label: 'Q.1.1.1',
        desc: 'Recording started correctly',
      },
      q_1_1_2: {
        ai: true,
        accuracy: 0.12,
        target: 's_1',
        type: 'positive',
        time: 10,
        label: 'Q.1.1.2',
        desc: 'Agent name and date provided',
      },
      q_2_1_1: {
        ai: true,
        accuracy: 0.87,
        target: 's_2',
        type: 'positive',
        time: 25,
        label: 'Q.2.1.1',
        desc: 'Customer provided personal ID info',
      },
      q_2_1_2: {
        ai: true,
        accuracy: 0.91,
        target: 's_2',
        type: 'positive',
        time: 30,
        label: 'Q.2.1.2',
        desc: 'Customer provided personal ID info',
      },
      q_2_1_3: {
        ai: true,
        accuracy: 0.4,
        target: 's_2',
        type: 'positive',
        time: 32,
        label: 'Q.2.1.3',
        desc: 'Only provided address',
      },
      q_2_2_1: {
        ai: true,
        accuracy: 0.88,
        target: 's_2',
        type: 'positive',
        time: 35,
        label: 'Q.2.2.1',
        desc: "Confirmed customer's investment experience",
      },
      q_2_2_2: {
        ai: true,
        accuracy: 0.48,
        target: 's_2',
        type: 'positive',
        time: 40,
        label: 'Q.2.2.2',
        desc: 'Witness verification done',
      },
      q_2_3_1: {
        ai: true,
        accuracy: 0.9,
        target: 's_2',
        type: 'positive',
        time: 45,
        label: 'Q.2.3.1',
        desc: 'Authorized person confirmed',
      },
      q_3_1_1: {
        ai: true,
        accuracy: 0.93,
        target: 's_3',
        type: 'positive',
        time: 50,
        label: 'Q.3.1.1',
        desc: 'Investment experience verified',
      },
      q_3_1_2: {
        ai: true,
        accuracy: 0.2,
        target: 's_3',
        type: 'positive',
        time: 55,
        label: 'Q.3.1.2',
        desc: "Not yet confirmed customer's non-vulnerable status",
      },
      q_4_1_1: {
        ai: true,
        accuracy: 0.91,
        target: 's_4',
        type: 'positive',
        time: 70,
        label: 'Q.4.1.1',
        desc: 'Witness name confirmed',
      },
      q_4_1_2: {
        ai: true,
        accuracy: 0.88,
        target: 's_4',
        type: 'positive',
        time: 75,
        label: 'Q.4.1.2',
        desc: 'Witness age confirmed',
      },
      q_4_1_3: {
        ai: true,
        accuracy: 0.95,
        target: 's_4',
        type: 'positive',
        time: 80,
        label: 'Q.4.1.3',
        desc: 'Witness education level confirmed',
      },
      q_4_1_4: {
        ai: true,
        accuracy: 0.43,
        target: 's_4',
        type: 'positive',
        time: 85,
        label: 'Q.4.1.4',
        desc: 'Failed to confirm witness understanding',
      },
      q_5_1_1: {
        ai: true,
        accuracy: 0.94,
        target: 's_5',
        type: 'positive',
        time: 90,
        label: 'Q.5.1.1',
        desc: "Authorized person's product knowledge confirmed",
      },
      q_5_1_2: {
        ai: true,
        accuracy: 0.89,
        target: 's_5',
        type: 'positive',
        time: 95,
        label: 'Q.5.1.2',
        desc: "Confirmed authorized person's understanding of derivatives",
      },
      q_6_1_1: {
        ai: true,
        accuracy: 0.92,
        target: 's_6',
        type: 'positive',
        time: 100,
        label: 'Q.6.1.1',
        desc: 'Customer confirmed as experienced in product category',
      },
      q_6_1_2: {
        ai: true,
        accuracy: 0.96,
        target: 's_6',
        type: 'positive',
        time: 105,
        label: 'Q.6.1.2',
        desc: "Confirmed customer's experience with similar investment products",
      },
      q_7_1_1: {
        ai: true,
        accuracy: 0.93,
        target: 's_7',
        type: 'positive',
        time: 110,
        label: 'Q.7.1.1',
        desc: 'Customer confirmed they had sufficient time to consider the investment',
      },
      q_7_1_2: {
        ai: true,
        accuracy: 0.47,
        target: 's_7',
        type: 'positive',
        time: 115,
        label: 'Q.7.1.2',
        desc: 'Customer did understand product characteristics and risks',
      },
      q_7_1_3: {
        ai: true,
        accuracy: 0.91,
        target: 's_7',
        type: 'positive',
        time: 120,
        label: 'Q.7.1.3',
        desc: 'Customer accepted potential losses associated with the product',
      },
      q_8_1_1: {
        ai: true,
        accuracy: 0.88,
        target: 's_8',
        type: 'positive',
        time: 125,
        label: 'Q.8.1.1',
        desc: 'Customer agreed with the suitability assessment results',
      },
      q_8_1_2: {
        ai: true,
        accuracy: 0.95,
        target: 's_8',
        type: 'positive',
        time: 130,
        label: 'Q.8.1.2',
        desc: 'Confirmed investment amount and account details',
      },
      q_8_1_3: {
        ai: true,
        accuracy: 0.97,
        target: 's_8',
        type: 'positive',
        time: 135,
        label: 'Q.8.1.3',
        desc: 'Customer confirmed readiness to proceed with the transaction',
      },
      q_8_1_4: {
        ai: true,
        accuracy: 0.52,
        target: 's_8',
        type: 'positive',
        time: 140,
        label: 'Q.8.1.4',
        desc: 'Customer did not understand the irrevocability of the application once accepted',
      },
      q_8_1_5: {
        ai: true,
        accuracy: 0.91,
        target: 's_8',
        type: 'positive',
        time: 145,
        label: 'Q.8.1.5',
        desc: 'Confirmed that customer was informed of product issuance results',
      },
    },
  },
  QADBMNLAM52MJ0GUEKVQ8I0ODO000003: {
    s_1: {
      ss_1_1: {
        q_1_1_1: '1_1_1_y',
        q_1_1_2: '1_1_2_n',
      },
      ss_1_2: {
        q_1_2_1: '1_2_1_n',
      },
      ss_1_3: {
        q_1_3_1: '1_3_1_n',
      },
    },
    s_2: {
      ss_2_1: {
        q_2_1_1: '2_1_1_y',
        q_2_1_2: '2_1_2_y',
        q_2_1_3: '2_1_3_n',
      },
      ss_2_2: {
        q_2_2_1: '2_2_1_n',
        q_2_2_2: '2_2_2_y',
      },
      ss_2_3: {
        q_2_3_1: '2_3_1_y',
      },
    },
    s_3: {
      ss_3_1: {
        q_3_1_1: '3_1_1_y',
      },
      ss_3_2: {
        q_3_2_1: '3_2_1_n',
      },
      ss_3_3: {
        q_3_3_1: '3_3_1_y',
        q_3_3_2: '3_3_2_y',
      },
    },
    s_4: {
      ss_4_1: {
        q4_1_1: 'Impolite closing.',
      },
    },
    s_5: {
      ss_5_1: {
        q5_1_1: '5_1_1_2',
      },
    },
    tags: {
      q_1_1_1: {
        target: 's_1',
        time: 10,
        type: 'positive',
        label: 'Q.1.1.1',
        desc: 'Name Provided',
      },
      q_1_1_2: {
        target: 's_1',
        time: 20,
        type: 'negative',
        label: 'Q.1.1.2',
        desc: 'No greetings',
      },
      q_1_2_1: {
        target: 's_1',
        time: 100,
        type: 'negative',
        label: 'Q.1.2.1',
        desc: 'Agent name not provided',
      },
      q_2_2_1: {
        target: 's_2',
        time: 130,
        type: 'negative',
        label: 'Q.2.2.1',
        desc: 'Not clarifying the problem',
      },
      q_3_2_1: {
        target: 's_3',
        time: 160,
        type: 'negative',
        label: 'Q.3.2.1',
        desc: 'Not asking for other problems',
      },
    },
  },
  QADBMNLAM52MJ0GUEKVQ8I0ODO000004: {
    s_1: {
      ss_1_1: {
        q_1_1_1: '1_1_1_y',
        q_1_1_2: '1_1_2_n',
      },
      ss_1_2: {
        q_1_2_1: '1_2_1_n',
      },
    },
    s_2: {
      ss_2_1: {
        q_2_1_1: '2_1_1_y',
        q_2_1_2: '2_1_2_y',
      },
    },
    s_3: {
      ss_3_1: {
        q_3_1_1: '3_1_1_y',
      },
      ss_3_2: {
        q_3_2_1: '3_2_1_y',
      },
      ss_3_3: {
        q_3_3_1: '3_3_1_y',
        q_3_3_2: '3_3_2_y',
      },
    },
  },
};
