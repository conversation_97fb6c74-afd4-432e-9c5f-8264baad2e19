import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    let yamlPath = path.join(
      process.cwd(),
      'apps/ctint-mf-template/public/config/ctint-global-config-dev.yaml'
    );
    if (!fs.existsSync(yamlPath)) {
      yamlPath = path.join(
        process.cwd(),
        'public/config/ctint-global-config-dev.yaml'
      );
      if (!fs.existsSync(yamlPath)) {
        throw new Error(`Configuration file not found: ${yamlPath}`);
      }
    }
    const fileContents = fs.readFileSync(yamlPath, 'utf8');
    const data = yaml.load(fileContents);

    res.status(200).json({
      isSuccess: true,
      data,
      error: null,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      isSuccess: false,
      error: 'Failed to load config',
    });
  }
}
