const { createGlobPatternsForDependencies } = require('@nx/react/tailwind');
import { GLOBAL_TAILWIND_CONFIG } from '../../libs/design-system/tailwind.config';
import path from 'path';

export const config = {
  ...GLOBAL_TAILWIND_CONFIG,
  content: [
    ...GLOBAL_TAILWIND_CONFIG.content,
    path.join(__dirname, './pages/**/*.{js,ts,jsx,tsx}'),
    path.join(__dirname, './components/**/*.{js,ts,jsx,tsx}'),
    path.join(__dirname, './app/**/*.{js,ts,jsx,tsx}'),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  
};

export default config;
