import {
  <PERSON>th<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>si<PERSON><PERSON><PERSON>le,
  ResizablePanel,
  ResizablePanelGroup,
} from '@cdss-modules/design-system';
import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import {
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system/components/_ui/Tabs';
import { triggers } from '.';

export function Main() {
  // const { toPath } = useRouteHandler();
  const GridData = (title: string, content: string) => {
    return (
      <div>
        <p className="mb-1 tfont">{title}:</p>
        <p>{content}</p>
      </div>
    );
  };
  return (
    <AuthChecker
      // requiredPemissions={{
      //   global: {
      //     portals: ['ctint-mf-template'],
      //   },
      //   user: {
      //     permissions: ['ctint-mf-template.application.visit'],
      //   },
      // }}
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      {/* <TemplateDemo
              testId="home"
              titleI18n="ctint-mf-template.templateHome.title"
              descI18n="ctint-mf-template.templateHome.desc"
              btnLabelI18n="ctint-mf-template.templateHome.btnLabel"
              onClickButton={() => toPath('/detail')}
            /> */}
      <div>
        <ResizablePanelGroup
          direction={'horizontal'}
          className="flex gap-4 w-full"
        >
          <ResizablePanel className="h-full">
            <div className="size-container">
              <Tabs triggers={triggers}>
                <TabsContent value={'profile'}>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-6 border-b pb-4 border-grey-200">
                      <Avatar
                        text="HY"
                        textClassName="text-black font-bold text-t5"
                        className="w-20"
                      />
                      <div className="text-t5 font-bold">HoYin (M)</div>
                      <div className="text-t5 font-bold">*********</div>
                    </div>
                    <div className="grid grid-cols-3 gap-x-6 gap-y-4">
                      <GridData />
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value={'history'}>history</TabsContent>
              </Tabs>
            </div>
          </ResizablePanel>
          <ResizableHandle />
          <ResizablePanel className="h-full">
            <div>2</div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </AuthChecker>
  );
}
