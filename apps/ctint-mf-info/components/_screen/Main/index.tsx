import {
  <PERSON>th<PERSON><PERSON><PERSON>,
  <PERSON>,
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  Tooltip,
  useRouteHandler,
} from '@cdss-modules/design-system';
import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';

import Icon from '@cdss-modules/design-system/components/_ui/Icon';
// import TemplateDemo from '../../_ui/TemplateDemo';
import {
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system/components/_ui/Tabs';

const triggers = [
  {
    value: 'profile',
    label: 'Profile',
  },
  {
    value: 'history',
    label: 'History',
  },
];

export const GridData = ({
  title,
  content,
}: {
  title: string;
  content: string;
}) => {
  return (
    <div>
      <p className="mb-1 font-bold whitespace-nowrap">{title}:</p>
      <p>{content}</p>
    </div>
  );
};

export function Main() {
  // const { toPath } = useRouteHandler();

  return (
    <AuthChecker
      // requiredPemissions={{
      //   global: {
      //     portals: ['ctint-mf-template'],
      //   },
      //   user: {
      //     permissions: ['ctint-mf-template.application.visit'],
      //   },
      // }}
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      {/* <TemplateDemo
        testId="home"
        titleI18n="ctint-mf-template.templateHome.title"
        descI18n="ctint-mf-template.templateHome.desc"
        btnLabelI18n="ctint-mf-template.templateHome.btnLabel"
        onClickButton={() => toPath('/detail')}
      /> */}
      <div className="flex flex-1 h-screen">
        <ResizablePanelGroup
          direction="vertical"
          className="flex flex-col gap-y-3 h-full"
        >
          <ResizablePanel className="h-full">
            <ResizablePanelGroup
              direction="horizontal"
              className="flex-1 flex gap-4 w-full h-full "
            >
              <ResizablePanel className="h-full">
                <div className="profile-panel">
                  <Tabs
                    triggers={triggers}
                    className="profile-panel__info"
                  >
                    <TabsContent value={'profile'}>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-6 pb-4 border-b border-grey-200">
                          <Avatar
                            text="HY"
                            textClassName="text-black font-bold text-t5"
                            className="w-20"
                          />
                          <div className="text-t5 font-bold">HoYin (M)</div>
                          <div className="text-t5 font-bold">C-1234567</div>
                        </div>
                        <div className="grid grid-cols-3 py-4 gap-x-6 gap-y-4 border-b border-grey-200">
                          <GridData
                            title={'HKID'}
                            content={'A1234567'}
                          />
                          <GridData
                            title={'Mobile No'}
                            content={'********'}
                          />
                          <GridData
                            title={'Account No'}
                            content={'D823020'}
                          />
                          <GridData
                            title={'Email'}
                            content={'<EMAIL>'}
                          />
                        </div>
                        <div className="grid grid-cols-3 py-4 gap-x-6 gap-y-4 border-b border-grey-200">
                          <GridData
                            title={'Station'}
                            content={'C727'}
                          />
                          <GridData
                            title={'Extension'}
                            content={'********'}
                          />
                          <GridData
                            title={'Workgroup'}
                            content={'Refund'}
                          />
                          <GridData
                            title={'Language'}
                            content={'Cantonese'}
                          />
                        </div>
                      </div>
                    </TabsContent>
                    <TabsContent value={'history'}>history</TabsContent>
                  </Tabs>
                  <div className="bg-white items-center w-full h-[120px] rounded-2xl gap-4 p-4 hidden profile-panel__toolbar">
                    <Avatar
                      text="HY"
                      textClassName="text-black font-bold text-body"
                      className="w-16"
                    />
                    <div className="block profile-panel__toolbar_name">
                      <div className="text-remark font-bold mb-1">
                        HoYin (M)
                      </div>
                      <div className="text-remark font-bold">C-1234567</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="size-4 rounded-full bg-status-success" />
                      <p>Avaliable</p>
                    </div>

                    <div className="grid grid-cols-2 gap-1 text-mini">
                      <GridData
                        title={'HKID'}
                        content={'A1234567'}
                      />
                      <GridData
                        title={'Mobile No'}
                        content={'********'}
                      />
                      <GridData
                        title={'Account No'}
                        content={'D823020'}
                      />
                      <GridData
                        title={'Email'}
                        content={'<EMAIL>'}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-1 text-mini profile-panel__toolbar_vrinfo">
                      <GridData
                        title={'Station'}
                        content={'C727'}
                      />
                      <GridData
                        title={'Extension'}
                        content={'********'}
                      />
                      <GridData
                        title={'Workgroup'}
                        content={'Refund'}
                      />
                      <GridData
                        title={'Language'}
                        content={'Cantonese'}
                      />
                    </div>
                  </div>
                </div>
              </ResizablePanel>
              <ResizableHandle />
              <ResizablePanel className="h-full">
                <div></div>
              </ResizablePanel>
            </ResizablePanelGroup>
          </ResizablePanel>
          <ResizableHandle />
          <ResizablePanel>
            <div className="w-full flex justify-center items-center bg-green-400">
              comingsoon
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </AuthChecker>
  );
}

export default Main;
