import { <PERSON><PERSON><PERSON><PERSON>, Toaster } from '@cdss-modules/design-system';
import Main from '../components/_screen/Main';
import AdminAuditScreen from '../components/_screen/Audit';
import { basePath, mfName } from '../lib/appConfig';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';

const Navigation = () => {
  const tabStyles = `
   flex-1 inline-flex items-center justify-center 
   py-4 px-6 
   font-medium text-sm 
   transition-all duration-200
   border-b-2 
   focus:outline-none focus:ring-2 focus:ring-orange-400 focus:ring-opacity-50
   whitespace-nowrap
   text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300
 `;

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto">
        <div className="flex border-b border-gray-200">
          <a
            href={`${basePath}/`}
            className={tabStyles}
          >
            <span className="inline-flex items-center">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              User Management
            </span>
          </a>
          <a
            href={`${basePath}/audit`}
            className={tabStyles}
          >
            <span className="inline-flex items-center">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                />
              </svg>
              Audit
            </span>
          </a>
        </div>
      </div>
    </nav>
  );
};

export const Page = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <main className="mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-4 sm:px-0 bg-white rounded-lg shadow-sm">
          <PageRenderer
            routes={[
              {
                path: '/',
                group: 'ctint-mf-user-admin',
                component: <Main />,
              },
              {
                path: '/audit',
                group: 'ctint-mf-user-admin',
                component: <AdminAuditScreen />,
              },
            ]}
            basePath={basePath}
          />
        </div>
      </main>
      <Toaster />
    </div>
  );
};

export const getServerSideProps = async () => {
  const globalConfig = loadGlobalConfig(mfName);
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      globalConfig,
      publicEnvVars,
    },
  };
};

export default Page;
