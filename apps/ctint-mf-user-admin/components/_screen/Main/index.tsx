/* eslint-disable @nx/enforce-module-boundaries */

import {
  CDSSAdminProvider,
  Panel,
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminUserList from '../../ui/AdminUserList';
import AdminUserGroupList from '../../ui/AdminUserGroupList';
import AdminUserRoleList from '../../ui/AdminUserRoleList';
import AdminUserPermissionList from '../../ui/AdminUserPermissionList';
import { Filter } from 'lucide-react';
import { useState } from 'react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import AdminFilter from '../../ui/AdminFilter';
import { useRole } from '@cdss-modules/design-system/context/RoleContext';
import { Condition } from '@cdss-modules/design-system/components/_ui/FilterComponent';
import {
  microfrontends,
  UserTabName,
} from 'apps/ctint-mf-user-admin/types/microfrontendsConfig';
import { ColumnFilter } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';

export const AdminUserBody = () => {
  const [filterOpen, setFilterOpen] = useState(false);
  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const userTabsCols: UserTabName[] =
    microfrontendsConfig['ctint-mf-user-admin']['user-tab-names'];
  const [customFilterValue, setCustomFilterValue] = useState<ColumnFilter[]>(
    []
  );
  const { t } = useTranslation();
  // const tableCols: Record<string, Condition> = {};
  // console.log(userTabsCols);

  const initTableColsData = userTabsCols.reduce(
    (acc, filter) => {
      acc[filter.value] = {
        ...filter,
        checked: false,
      }; // Initialize all filter values to empty
      return acc;
    },
    {} as Record<string, Condition>
  );

  // console.log(initTableColsData);

  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto">
      <Panel containerClassName="h-full">
        <Tabs
          defaultTab={'users'}
          triggers={[
            {
              value: 'users',
              label: t('ctint-mf-user-admin.tab.user'),
            },
            // {
            //   value: 'user-groups',
            //   label: 'User Groups',
            // },
            // {
            //   value: 'roles',
            //   label: 'Roles',
            // },
            // {
            //   value: 'permissions',
            //   label: 'Permissions',
            // },
          ]}
          triggerClassName="py-2 px-2 text-body"
          rightPanel={
            <div className="flex items-center pr-2">
              <button
                className={cn(
                  'hover:text-primary',
                  filterOpen && 'text-primary'
                )}
                type="button"
                onClick={() => {
                  setFilterOpen(!filterOpen);
                }}
              >
                <Filter />
              </button>
            </div>
          }
        >
          {filterOpen && (
            <AdminFilter
              cols={initTableColsData}
              customApplyFilter={(data: Condition) => {
                const result = Object.entries(data)
                  .filter(([_, item]) => item.checked === true && item.data)
                  .map(([key, item]) => ({
                    id: key,
                    value: item.data,
                  })) as ColumnFilter[];

                // console.log(result);
                setCustomFilterValue(result);
              }}
              customClearFilter={() => {
                setCustomFilterValue([]);
              }}
            />
          )}
          <TabsContent
            value={'users'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserList
              cols={initTableColsData}
              customFilterValue={customFilterValue}
              setFilterOpen={setFilterOpen}
            />
          </TabsContent>
          <TabsContent
            value={'user-groups'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserGroupList
              inDetail={undefined}
              isEditing={false}
            />
          </TabsContent>
          <TabsContent
            value={'roles'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserRoleList
              inDetail={undefined}
              isEditing={false}
            />
          </TabsContent>
          <TabsContent
            value={'permissions'}
            className="p-0 h-0 flex-1 flex flex-col"
          >
            <AdminUserPermissionList />
          </TabsContent>
        </Tabs>
      </Panel>
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

const AdminUser = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <CDSSAdminProvider>
        <AdminUserBody />
      </CDSSAdminProvider>
    </QueryClientProvider>
  );
};

export default AdminUser;
