import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { Controller, useFormContext } from 'react-hook-form';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { formatDateTime } from '../AdminUserList';
import { optionsMap } from '@cdss-modules/design-system/components/_ui/FilterComponent/config';
import { useTranslation } from 'react-i18next';

// import { Select } from '@radix-ui/react-select';
export const EditableField = ({
  combinedData,
  isEditing,
  ...thisField
}: any) => {
  const { control } = useFormContext();
  const { t } = useTranslation();

  const handleInvalid = (e: React.InvalidEvent<HTMLInputElement>) => {
    e.target.setCustomValidity(t('ctint-mf-user-admin.form.invaildRequired'));
  };

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    // 清空錯誤信息
    e.target.setCustomValidity('');
  };

  return (
    <div className="flex flex-wrap gap-x-6 w-[450px]">
      <Field
        title={thisField?.title}
        icon={<Icon name="error" />}
        placeholder={thisField?.placeholder || 'Please enter'}
      >
        <Controller
          name={thisField?.name || ''}
          control={control}
          // rules={{ required: true }}
          render={({ field }) => (
            <>
              {/* {thisField?.name || ''} */}
              {isEditing ? (
                (() => {
                  if (thisField?.filterType === 'select') {
                    const getOptions = () => {
                      const options = [];
                      if (optionsMap[thisField?.name]) {
                        for (const item of optionsMap[thisField?.name]) {
                          options.push({
                            id: item.value,
                            label: item.labelEn,
                            value: item.value,
                          });
                        }
                      }
                      return options;
                    };

                    return (
                      <Select
                        placeholder="Select Options"
                        options={getOptions()}
                        labelClassName="h-full text-remark"
                        labelContainerClassName="h-8 w-50"
                        isPagination={false}
                        {...field}
                      />
                    );
                  } else if (thisField?.filterType === 'multipleSelect') {
                    const options = () => {
                      const selectOptionData =
                        combinedData[thisField?.name] || [];
                      // console.log(thisField?.name);
                      // console.log(selectOptionData);

                      return selectOptionData.map((opt: any) => ({
                        id: opt.name,
                        label: opt.name,
                        value: opt.name,
                      }));
                    };

                    return (
                      <Select
                        {...field}
                        placeholder="Select Options"
                        mode="multiple"
                        showSearch={true}
                        labelClassName="h-full text-remark"
                        labelContainerClassName="h-8 w-50"
                        isPagination={false}
                        options={options()}
                        onChange={(e) => {
                          const isSelected = e?.target.checked;
                          const value = e?.target.value;
                          const prevValues = field?.value || [];

                          if (isSelected) {
                            field?.onChange([...prevValues, value]);
                          } else {
                            field?.onChange(
                              prevValues?.filter(
                                (prev: string) => prev !== value
                              )
                            );
                          }
                        }}
                        value={field?.value || []}
                      />
                    );
                  } else {
                    return (
                      <Input
                        disabled={thisField?.readOnly}
                        size="s"
                        required={thisField?.require}
                        onInvalid={handleInvalid}
                        onInput={handleInput}
                        {...field}
                      />
                    );
                  }
                })()
              ) : (
                <span>
                  {(() => {
                    const value = thisField?.value || field?.value;

                    if (thisField?.filterType === 'dateRange' && value) {
                      return formatDateTime(value);
                    }

                    return value || 'N/A';
                  })()}
                </span>
              )}
            </>
          )}
        />
      </Field>
    </div>
  );
};

export default EditableField;
