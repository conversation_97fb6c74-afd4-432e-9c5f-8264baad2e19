import {
  DataTable,
  PaginationConfig,
} from '@cdss-modules/design-system/components/_ui/DataTable';
import { useEffect, useState } from 'react';
import {
  ColumnDef,
  ColumnFilter,
  Table as TableType,
} from '@tanstack/react-table';
import {
  Sorting<PERSON>utton,
  Tooltip,
  useCD<PERSON>Admin,
  useRouteHand<PERSON>,
} from '@cdss-modules/design-system';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { Plus } from 'lucide-react';
import { AdminUserDetail } from '../AdminUserDetail';
import { Condition } from '@cdss-modules/design-system/components/_ui/FilterComponent';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { GetUserList } from '../../../lib/api';
import {
  TAdminUserData,
  TAdminUserDataResp,
} from '../../../types/microfrontendsConfig';
import { useTranslation } from 'react-i18next';
import { generateColumns } from './generateColumns';

export type AdminUserListProps = {
  cols: Record<string, Condition>;
  customFilterValue: ColumnFilter[];
  setFilterOpen: (isOpen: boolean) => void;
};

export type AdminFilterProps = {
  customApplyFilter: (data: any) => void;
  customClearFilter: () => void;
  cols: Record<string, Condition>;
};
export type TcustmCol = {
  name: string;
  key: string;
  readOnly: boolean;
  filterType: string;
  require: boolean;
};
//type TAdminUserDataColumn = keyof TAdminUserData;

export const AdminUserList = ({
  cols,
  customFilterValue,
  setFilterOpen,
}: AdminUserListProps) => {
  const { users, openedEntity, updateOpenedEntity } = useCDSSAdmin();
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TAdminUserData>>();
  const [sortOrder, setSortOrder] = useState<any>();
  const [userList, setUserList] = useState<TAdminUserData[]>([]);
  const { basePath } = useRouteHandler();
  const { i18n } = useTranslation();

  const paginationConfig: PaginationConfig = {
    enable: true,
    pageIndex: 0,
    pageSize: 10,
  };
  useEffect(() => {
    getUserList();
  }, []);

  const getUserList = async () => {
    const result = await GetUserList(basePath);
    const respData: TAdminUserDataResp = result.data;
    const userList = flatUserListDataForRoleNGroup(respData.data);
    setUserList(userList);
  };

  const flatUserListDataForRoleNGroup = (
    userList: TAdminUserData[]
  ): TAdminUserData[] => {
    return userList.map((item) => {
      const roleNames = item.roles?.map((role) => role.name).join(',');
      const groupNames = item.groups?.map((group) => group.name).join(',');
      return {
        ...item,
        roleNames,
        groupNames,
      };
    });
  };

  const showColumns: string[] = [];
  const showColumnsKey: string[] = [];
  const customCols: TcustmCol[] = [];

  Object.entries(cols).forEach(([key, item]) => {
    const custmCol: TcustmCol = {
      name: i18n.language == 'en' ? item.labelEn : item.labelCh,
      key: key,
      readOnly: item.readOnly,
      filterType: item.filterType,
      require: item.require,
    };
    if (item.active) {
      showColumnsKey.push(key);
      showColumns.push(i18n.language == 'en' ? item.labelEn : item.labelCh);
      customCols.push(custmCol);
    }
  });

  const setOpenedEntity = (entity: any) => {
    if (!entity) {
      updateOpenedEntity(null);
      return;
    }
    //console.log(entity);
    updateOpenedEntity({
      type: 'user',
      entity,
    });
    setFilterOpen(false);
  };

  return (
    <div className="px-4 pt-1 pb-6 flex flex-col h-full gap-y-4 overflow-auto">
      {/* {console.log(openedEntity)} */}
      {openedEntity && openedEntity?.type === 'user' ? (
        <AdminUserDetail
          showCols={customCols}
          setOpenedEntity={setOpenedEntity}
        />
      ) : (
        <div className="flex-1 h-0">
          <DataTable<TAdminUserData>
            data={userList}
            columns={
              generateColumns(
                showColumnsKey,
                showColumns,
                [],
                sortOrder,
                (input) => {
                  setSortOrder(input);
                },
                (id?: string) => {
                  setOpenedEntity(
                    userList?.find((d) => d.id === `${id}`) || {}
                  );
                }
              ) as any
            }
            loading={false}
            emptyMessage="No data found"
            // error={error?.message}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onClickRow={(row) => {
              // console.log(userList?.[row?.index || 0] || {});
              setOpenedEntity(userList?.[row?.index || 0] || {});
            }}
            onTableSetUp={(table) => setTable(table)}
            paginationConfig={paginationConfig}
            customFilterValue={customFilterValue}
          />
        </div>
      )}
    </div>
  );
};

export const formatDateTime = (dateString?: string): string => {
  const date = dateString ? new Date(dateString) : new Date();
  const formatNumber = (n: number): string => String(n).padStart(2, '0');

  const year = date.getFullYear();
  const month = formatNumber(date.getMonth() + 1);
  const day = formatNumber(date.getDate());
  const hours = formatNumber(date.getHours());
  const minutes = formatNumber(date.getMinutes());
  const seconds = formatNumber(date.getSeconds());

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
export default AdminUserList;
