import React, { useState, KeyboardEvent, useRef, useEffect } from 'react';
import IconBase from '../../../public/iconsConfig.json';
import EmojiPicker from './EmojiPicker';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
import { useRouteHandler } from '@cdss-modules/design-system';
import { v4 as uuidv4 } from 'uuid';
import SendButton from './SendButton';
import DropzoneAttachmentButton from '@cdss-modules/design-system/components/_ui/DropzoneAttachmentButton';
import { useMessageSender } from '../../../hooks/useMessageSender';
import IconCRT from '@cdss-modules/design-system/components/_ui/Icon/IconCRT';
import TemplateSelector from '@cdss-modules/design-system/components/_ui/CannedResponses';
// @ts-ignore
import { useConversationStore } from 'cdss/store/conversation';
interface ChatToolProps {
  onToggleTemplate: () => void;
  onToggleRecord: () => void;
  onExpandChange: (isExpanded: boolean) => void;
  isExpanded: boolean;
  conversationId: string;
  isConnected: boolean;
  isTransferring: boolean;
  showAttachmentPage?: boolean;
  onToggleAttachmentPage: () => void;
  whatsAppCustomer?: Customer;
  isWindowExpired: boolean;
  countdownTime: string;
}

const ChatTool: React.FC<ChatToolProps> = ({
  onToggleTemplate,
  onToggleRecord,
  onExpandChange,
  isExpanded,
  conversationId,
  isConnected,
  isTransferring,
  onToggleAttachmentPage,
  whatsAppCustomer,
  isWindowExpired,
  countdownTime,
}) => {
  const [message, setMessage] = useState('');
  const [isButtonLoading, setIsButtonLoading] = useState(false);
  const [cooldownTime, setCooldownTime] = useState(0);
  const { basePath } = useRouteHandler();
  const { sendTextMessage } = useMessageSender(basePath);
  const lastProcessTime = useRef<number>(0);
  const cooldownTimer = useRef<NodeJS.Timeout | null>(null);
  const [isDNC, setDNC] = useState<boolean>(false);
  // canned response Template
  const [CRTActive, setCRTActive] = useState<boolean>(false);
  //获取store的值 每次变化都要触发页面渲染 不符合要求(有条件过滤) 要用订阅模式
  const saaContent = useConversationStore((state: any) => state.SAAContent);

  // Calculate placeholder text
  const getPlaceholder = () => {
    if (isWindowExpired) {
      return 'The conversation window is closed';
    } else if (isTransferring || isConnected) {
      return 'Please enter your content press shift and enter to complete the line';
    } else {
      return 'Please Wrap Up';
    }
  };

  const placeholder = getPlaceholder();
  // 监听复制内容的变化
  useEffect(() => {
    // 专门订阅 SAAContent 的变化
    const unsubscribe = useConversationStore.subscribe(
      (state: any) => state.SAAContent,
      (newSAAContent: any) => {
        if (newSAAContent && !isWindowExpired) {
          setMessage(newSAAContent);
          console.log('SAAContent 更新:', newSAAContent);
        }
      }
    );

    // 返回清理函数
    return unsubscribe;
  }, [isWindowExpired]); // 只依赖于 isWindowExpired
  // 使用 useEffect 强制处理会话切换
  useEffect(() => {
    // 重置消息
    setMessage('');

    // 重置计时器
    if (cooldownTimer.current) {
      clearTimeout(cooldownTimer.current);
    }
  }, [conversationId]); // 仅在会话 ID 变化时执行

  const startCooldown = (waitTime: number) => {
    setIsButtonLoading(true);
    setCooldownTime(waitTime);

    const startTime = Date.now();
    const updateCooldown = () => {
      const elapsed = Date.now() - startTime;
      const remaining = waitTime - elapsed;

      if (remaining <= 0) {
        setIsButtonLoading(false);
        setCooldownTime(0);
        if (cooldownTimer.current) {
          clearTimeout(cooldownTimer.current);
        }
      } else {
        setCooldownTime(remaining);
        cooldownTimer.current = setTimeout(updateCooldown, 100);
      }
    };

    cooldownTimer.current = setTimeout(updateCooldown, 100);
  };

  const calculateWaitTime = (currentTime: number): number => {
    const timeSinceLastProcess = currentTime - lastProcessTime.current;

    if (timeSinceLastProcess >= 3000) {
      return 0;
    } else {
      return 3000 - timeSinceLastProcess;
    }
  };

  const handleExpandToggle = () => {
    onExpandChange(!isExpanded);
  };

  const handleSendMessage = async (message: string) => {
    // Prevent sending message if the window is expired
    if (isWindowExpired) {
      return;
    }

    const currentTime = Date.now();
    const waitTime = calculateWaitTime(currentTime);

    if (waitTime > 0) {
      startCooldown(waitTime);
      return;
    }

    // Only clean whitespace at the beginning and end, preserve line breaks
    const re_textbody = message.replace(/^\s+|\s+$/g, '');
    if (re_textbody) {
      try {
        // 先记录处理时间并清空输入框
        lastProcessTime.current = Date.now();
        setMessage(''); // Clear input immediately after sending

        // 发送消息
        await sendTextMessage(conversationId, re_textbody);
      } catch (error) {
        console.error('Failed to send message:', error);
      }
    }
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    // Prevent input if window is expired
    if (isWindowExpired) {
      e.preventDefault();
      return;
    }

    if (e.key === 'Enter') {
      if (e.shiftKey) {
        return;
      } else {
        e.preventDefault();
        handleSendMessage(message);
      }
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    // Prevent adding emoji if window is expired
    if (isWindowExpired) {
      return;
    }
    setMessage((prevMessage) => prevMessage + emoji);
  };

  const toggleCRT = () => {
    setCRTActive(!CRTActive);
    // 可以在这里添加其他逻辑，如打开聊天窗口
  };
  const handleCRTSelect = (content: string, templateId: string) => {
    // Prevent setting template content if window is expired
    if (isWindowExpired) {
      return;
    }

    // Set the template content as the message
    setMessage(content);

    // Close the template selector
    setCRTActive(false);
  };
  // Conditionally render UI elements based on window status
  const renderActionButtons = () => {
    return (
      <>
        <EmojiPicker onSelect={handleEmojiSelect} />
        <DropzoneAttachmentButton
          onToggleAttachmentPage={onToggleAttachmentPage}
          disabled={isWindowExpired}
        />
        <TemplateSelector
          isOpen={CRTActive}
          onSelectTemplate={handleCRTSelect}
          onClose={() => {
            setCRTActive(false);
          }}
          spaceType={'messageCannedResponse'}
          contentType={'text'}
          showNotes={false}
        ></TemplateSelector>
        {isExpanded && (
          <>
            <IconCRT
              size={'24px'}
              className="hover:opacity-80 transition-opacity"
              isActive={CRTActive}
              onClick={() => toggleCRT()}
              isDisable={isWindowExpired}
            ></IconCRT>
            <button
              className="flex items-center justify-center hover:cursor-pointer hover:bg-gray-200 rounded-lg"
              onClick={onToggleTemplate}
            >
              <img
                src={IconBase.templateMessage}
                alt="template"
                className="w-8 h-8"
              />
            </button>
            <button
              className="flex items-center justify-center hover:cursor-pointer hover:bg-gray-200 rounded-lg"
              onClick={onToggleRecord}
            >
              <img
                src={IconBase.sessionRecord}
                alt="record"
                className="w-8 h-8"
              />
            </button>
          </>
        )}
        <button
          className="flex items-center justify-center hover:cursor-pointer"
          onClick={handleExpandToggle}
        >
          <img
            src={IconBase.chatToolMore}
            alt="more"
            className="w-8 h-8"
          />
        </button>
        <SendButton
          onClick={() => handleSendMessage(message)}
          isButtonLoading={isButtonLoading}
        />
      </>
    );
  };

  return !isConnected ? (
    <></>
  ) : (
    <div className="p-4">
      <div className="bg-gray-100 rounded-md p-2 mb-2 flex items-center justify-center gap-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 text-gray-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span className="text-sm text-gray-700">
          {isWindowExpired
            ? 'The conversation window is closed'
            : `The conversation window will be closed in: ${countdownTime || '00:00:00'}`}
        </span>
      </div>

      <div className="flex flex-col">
        {isExpanded ? (
          <div
            className={`bg-white border border-gray-200 rounded-lg ${isWindowExpired ? 'opacity-70' : ''}`}
          >
            <div className="flex-1 p-4">
              <textarea
                className="bg-transparent outline-none w-full resize-none h-20"
                placeholder={placeholder}
                value={message}
                onChange={(e) => !isWindowExpired && setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                aria-label="Message input"
                disabled={isWindowExpired}
              />
            </div>
            <div className="border-gray-200 p-2">
              <div className="flex justify-end items-center space-x-2">
                {renderActionButtons()}
              </div>
            </div>
          </div>
        ) : (
          <div
            className={`bg-white border border-gray-200 rounded-lg p-2 flex items-center ${isWindowExpired ? 'opacity-70' : ''}`}
          >
            <div className="flex-1">
              <textarea
                className="bg-transparent outline-none w-full resize-none"
                style={{ height: '24px' }}
                placeholder={placeholder}
                value={message}
                onChange={(e) => !isWindowExpired && setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                aria-label="Message input"
                disabled={isWindowExpired}
              />
            </div>
            <div className="flex items-center space-x-2 ml-2">
              {renderActionButtons()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatTool;
