import React from 'react';
import TransferButton from 'apps/ctint-mf-message/components/_ui/chatWindow/TransferButton';
import WrapUpButton from 'apps/ctint-mf-message/components/_ui/chatWindow/WrapUpButton';
import TimerWithBorder from './timerWithBorder';
import { User } from '../../../@types/microFrontendConfigs';
import EndMessageButtion from './EndMessageButtion';

interface ChatWindowHeaderProps {
  username: string;
  convST?: Date;
  convET?: Date;
  onTransfer: (type: string, id: string, transferType?: string) => void;
  onWrapUp: () => void;
  showWrapUp: boolean;
  isConnected?: boolean;
  isTransferring?: boolean;
  transferring?: boolean;
  selectedUser?: User | null;
  onCancelTransfer?: () => void;
  onEndMessage: () => void;
  isEnding: boolean;
  isWrapUpping: boolean;
  conversationId?: string;
}

const ChatWindowHeader: React.FC<ChatWindowHeaderProps> = ({
  username,
  convST,
  convET,
  onTransfer,
  onWrapUp,
  showWrapUp,
  isConnected,
  isTransferring,
  onCancelTransfer,
  selectedUser,
  onEndMessage,
  isEnding,
  isWrapUpping,
  conversationId,
}) => {
  const getInitials = (
    username: string | undefined
  ): { initials: string; isNormalName: boolean } => {
    if (!username || username.trim() === '') {
      return { initials: '', isNormalName: false };
    }

    // 移除表情符号
    const removeEmojis = (str: string): string => {
      return str.replace(
        /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu,
        ''
      );
    };

    // 处理后的用户名
    const cleanUsername = removeEmojis(username).trim();
    if (cleanUsername === '') {
      return { initials: '', isNormalName: false };
    }

    // 检测是否为电话号码或包含数字
    if (/\d/.test(cleanUsername)) {
      return { initials: '', isNormalName: false };
    }

    // 检测是否包含中文字符
    const containsChinese = /[\u4e00-\u9fa5]/.test(cleanUsername);

    if (containsChinese) {
      // 中文名字：只取第一个字
      return { initials: cleanUsername.charAt(0), isNormalName: true };
    }

    // 检测是否为英文名（只包含字母和空格）
    if (!/^[A-Za-z\s]+$/.test(cleanUsername)) {
      // 不是英文名
      return { initials: '', isNormalName: false };
    }

    // 处理英文名
    const parts = cleanUsername.split(' ').filter((part) => part.length > 0);

    if (parts.length > 1) {
      // 有空格：取第一段和最后一段的第一个字母
      const firstLetter = parts[0].charAt(0) || '';
      const lastLetter = parts[parts.length - 1].charAt(0) || '';
      return {
        initials: (firstLetter + lastLetter).toUpperCase(),
        isNormalName: true,
      };
    } else if (parts.length === 1) {
      // 没有空格：取第一个字母
      return {
        initials: parts[0].charAt(0).toUpperCase(),
        isNormalName: true,
      };
    }

    return { initials: '', isNormalName: false };
  };
  const { initials, isNormalName } = getInitials(username);
  return (
    <div className="flex items-center justify-between p-4 border-b">
      <div className="flex items-center gap-3">
        <div className="w-12 h-12 rounded-full border flex items-center justify-center">
          {isNormalName ? (
            <span className="font-semibold">{initials}</span>
          ) : (
            <svg
              width="24"
              height="24"
              viewBox="0 0 17 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16.9989 16.5501C17.0205 17.4309 16.7279 18.2915 16.1716 18.9834C15.9425 19.2956 15.6414 19.5504 15.2929 19.7271C14.9443 19.9037 14.5582 19.9972 14.1659 20H2.83387C2.44242 19.9968 2.05725 19.9031 1.7097 19.7264C1.36216 19.5498 1.06205 19.2952 0.833774 18.9834C0.276286 18.2918 -0.0182065 17.4314 0.000872024 16.5501C-0.00387334 15.8467 0.0339664 15.1436 0.114192 14.4446C0.192295 13.7724 0.332636 13.1086 0.533477 12.4613C0.711295 11.8676 0.972551 11.3011 1.30972 10.778C1.62564 10.2997 2.05286 9.90178 2.55624 9.61694C3.10149 9.31318 3.71964 9.15783 4.3467 9.16696C4.88931 9.70402 5.53672 10.1284 6.25028 10.4147C6.96384 10.7011 7.72893 10.8435 8.49988 10.8336C9.27177 10.8443 10.0379 10.7022 10.7525 10.4158C11.4671 10.1295 12.1154 9.7047 12.6587 9.16696C13.2861 9.15536 13.9049 9.31088 14.4492 9.61694C14.9513 9.90346 15.3781 10.3011 15.6957 10.778C16.0343 11.3063 16.2956 11.8785 16.4719 12.478C16.6723 13.1197 16.8127 13.7779 16.8912 14.4446C16.9666 15.1439 17.0026 15.8469 16.9989 16.5501ZM12.1035 1.46716C12.5857 1.9247 12.9672 2.47425 13.2245 3.08193C13.4817 3.68961 13.6093 4.34252 13.5993 5.0004C13.6093 5.65828 13.4817 6.3112 13.2245 6.91887C12.9672 7.52655 12.5857 8.07611 12.1035 8.53364C11.6368 9.00647 11.0763 9.38054 10.4565 9.63278C9.83677 9.88503 9.17085 10.0101 8.49988 10.0003C7.8289 10.0101 7.16299 9.88503 6.54321 9.63278C5.92344 9.38054 5.36294 9.00647 4.8963 8.53364C4.41406 8.07611 4.03254 7.52655 3.77528 6.91887C3.51801 6.3112 3.39045 5.65828 3.40047 5.0004C3.39045 4.34252 3.51801 3.68961 3.77528 3.08193C4.03254 2.47425 4.41406 1.9247 4.8963 1.46716C5.36294 0.994339 5.92344 0.620267 6.54321 0.368024C7.16299 0.11578 7.8289 -0.00928732 8.49988 0.000537013C9.17085 -0.00928732 9.83677 0.11578 10.4565 0.368024C11.0763 0.620267 11.6368 0.994339 12.1035 1.46716Z"
                fill="#DEDEDE"
              />
            </svg>
          )}
        </div>
        <div>
          <h2 className="font-semibold">{username}</h2>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <TimerWithBorder
          startTime={convST?.toISOString()}
          endTime={convET?.toISOString()}
          isConnected={isConnected}
        />
        <div className="flex gap-2">
          <EndMessageButtion
            onClick={onEndMessage}
            disabled={!isConnected}
            isEnding={isEnding}
          />
          <WrapUpButton
            isActive={showWrapUp}
            onClick={onWrapUp}
            disabled={!isConnected}
            isWrapUpping={isWrapUpping}
          />
          <TransferButton
            onTransfer={onTransfer}
            disabled={!isConnected}
            transferring={isTransferring}
            selectedUser={selectedUser}
            onCancelTransfer={onCancelTransfer}
            conversationId={conversationId}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatWindowHeader;
