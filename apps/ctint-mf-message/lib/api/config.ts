export const apiConfig = {
  paths: {
    conversation: { conversations: '/api/conversations' },
    message: { messages: '/api/messages' },
    detail: {
      info: '/api/process-api/ctint-conv/interaction/detail',
      media: '/api/process-api/ctint-conv/interaction/recordings',
      transcript:
        '/api/process-api/ctint-conv/interaction/recordings/transcript',
      evaluation_form: '/api/process-api/ctint-qm/sop/form/list',
      evaluation_list: '/api/process-api/ctint-qm/inspection/evaluation/list',
      evaluation_assign: '/api/process-api/ctint-qm/inspection/evaluation',
      evaluation_nlp_result_update:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail',
      evaluation_nlp_result_list:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail/list',
      evaluation_result_update:
        '/api/process-api/ctint-qm/inspection/evaluation/detail',
      stand_script_result: '/api/process-api/ctint-qm/sop/standardScript/list',
    },
    recordings: '/api/process-api/ctint-conv/recordings',
    export: '/api/process-api/ctint-conv/interaction',
    gc_recordings: '/api/process-api/ctint-conv/interactions',
    sort: 'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings',
    config: '/api/process-api/ctint-config/userconfig',
    transcript:
      'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings/transcript',
    getMessages:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}',
    sendMessage:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}/messages',
    markRead:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}/messages',
    uploadMedia:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}/messages/media',
    msgTransfer:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}/participants/{participantId}/replace',
    msgTransferCancel:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}/participants/{participantId}/disconnect',
    supportedContent:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/conversations/messaging/supportedcontent/{supportedContentId}',
    users: {
      searchUsers: '/api/process-api/ctint-user/users/search',
      getAllUsers: '/api/process-api/ctint-user/users',
    },
    templateMsg: {
      getTemplateList:
        '/api/process-api/ctint-message/api/v1/external/whatsapp/templates',
    },
    callControl: {
      msgWrapUp:
        '/api/process-api/ctint-call-control/conversations/messages/participants',
    },
    ICRM: {
      getCustomers: '/api/process-api/ctint-user/customer/list',
    },
  },
};
