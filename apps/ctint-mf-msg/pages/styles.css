@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body{
    @apply bg-common-bg;
  }

  /* width */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    @apply bg-common-divider rounded-full;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    @apply bg-primary rounded-full;
    background-clip: padding-box;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-black;
  }
}

@layer components {

  @keyframes spinner-animation {
    0% {
      stroke-dasharray: 1 98;
      stroke-dashoffset: -105;
    }
    50% {
      stroke-dasharray: 80 10;
      stroke-dashoffset: -160;
    }
    100% {
      stroke-dasharray: 1 98;
      stroke-dashoffset: -300;
    }
  }
  .spinner-animation {
    transform-origin: center;
    animation-name: animation;
    animation: spinner-animation 1.2s infinite;
  }
  .saa-keyword {
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
  }
  .saa-keyword:hover {
    @apply text-primary;
  }
  .saa-article h2{
    @apply text-body font-bold mb-4;
  }
  .saa-article p{
    @apply text-remark mb-4;
  }
  .email-preview{
    h1{
      @apply text-body font-bold;
    }
    p{
      @apply text-remark mb-2;
    }
  }
}

.ql-container {
  height: auto !important;
}

.ql-editor {
  height: auto !important;
}

.ql-container .ql-editor h1{
  font-weight:bold;
}
.ql-container .ql-editor p{
  margin-bottom: 16px;
}