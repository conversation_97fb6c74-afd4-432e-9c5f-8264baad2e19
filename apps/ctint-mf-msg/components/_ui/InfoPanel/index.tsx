import { Tabs, TabsContent, useCDSS } from '@cdss-modules/design-system';
import BasicHistory from '../BasicHistory';
import BasicInfo from '../BasicInfo';
import dayjs from 'dayjs';
import { GLOBAL_DATE_FORMAT } from '@cdss-modules/design-system/lib/constants';

const DUMMY_DETAIL = {
  // id: 'E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T',
  conversationId: '-',
  startTime: '2024-03-22T02:57:28.000+0000',
  endTime: '2024-03-22T02:58:08.000+0000',
  mediaUri:
    '/recordings/E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T/play/a5d659f4-c5c7-472e-9a7a-f9dcdafc44cf.mp3',
  duration: 40.187,
  users: null,
  direction: 'Internal',
  mediaType: 'call',
  username: 'MOCK USER',
  dialedNumber: '22001',
  callerNumber: '71002',
} as any;

export const InfoPanel = () => {
  const { activeInteraction } = useCDSS();
  return (
    <Tabs
      defaultTab={'info'}
      triggers={[
        {
          value: 'info',
          label: 'Info',
        },
        {
          value: 'history',
          label: 'History',
        },
      ]}
      triggerClassName="py-2 px-2 text-body"
    >
      <TabsContent
        value={'info'}
        className="p-4 h-0 flex-1 flex flex-col"
      >
        <BasicInfo
          data={[
            {
              label: 'Customer Name',
              value: activeInteraction?.customerName,
              icon: 'user',
            },
            {
              label: 'Gender',
              value: activeInteraction?.gender ?? 'Male',
              icon: 'file',
            },
            {
              label: 'Preferred Title',
              value: activeInteraction?.preferredTitle ?? 'Mr.',
              icon: 'file',
            },
            {
              label: 'Language',
              value: activeInteraction?.language ?? 'Cantonese',
              icon: 'file',
            },
            {
              label: 'Contact Number',
              value: '+852 5432 1234',
              icon: 'inbound',
            },
            {
              label: 'Email',
              value: 'N/A',
              icon: 'file',
            },
            {
              label: 'Member since',
              value: dayjs('2010-03-22T02:57:28.000+0000').format(
                GLOBAL_DATE_FORMAT
              ),
              icon: 'calendar',
            },
            {
              label: 'Birthday',
              value: dayjs('1990-01-15T02:57:28.000+0000').format(
                GLOBAL_DATE_FORMAT
              ),
              icon: 'calendar',
            },
          ]}
        />
      </TabsContent>
      <TabsContent
        value={'history'}
        className="h-0 flex-1 flex flex-col"
      >
        <BasicHistory />
      </TabsContent>
    </Tabs>
  );
};

export default InfoPanel;
