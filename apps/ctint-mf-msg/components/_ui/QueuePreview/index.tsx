import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { FormProvider, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button } from '@cdss-modules/design-system';
import EmailViewer from '../EmailViewer';
import VoiceMailViewer from '../VoiceMailViewer';

export type TQueuePreviewProps = {
  data: any;
  onOpenChange: (open: boolean) => void;
};
export type TSOPRule = {
  id?: string;
  key?: string;
  name?: string;
  dataType?: 'text' | 'metadata' | 'dict' | 'rule-set';
  dataKey?: string;
  dataValue?: string;
  relation?: 'exists' | 'equals' | 'all' | 'numberof';
  relationFactor?: number;
  ruleSet?: string[];
};

const ruleSchema = yup
  .object({
    data: yup.object({
      id: yup.string(),
      key: yup.string(),
      name: yup.string(),
      dataType: yup.string(),
      dataKey: yup.string(),
      dataValue: yup.string(),
      relation: yup.string(),
      relationFactor: yup.number(),
      ruleSet: yup.array().of(yup.string()),
    }),
  })
  .required();
export const QueuePreview = ({ data, onOpenChange }: TQueuePreviewProps) => {
  const methods = useForm({
    resolver: yupResolver(ruleSchema),
    defaultValues: { data: { ...data } },
  });
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;

  const onSubmit = async (data: any) => {
    window.alert(`Submitted data: ${JSON.stringify(data)}`);
  };
  return (
    <Popup
      open={!!data}
      onOpenChange={onOpenChange}
    >
      <PopupContent
        className="w-4/5 max-w-[700px] shadow-md"
        title={`Queue Preview: ${data?.id}`}
      >
        <FormProvider {...methods}>
          {data && (
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col w-full px-4 pb-6"
            >
              {data?.type === 'email' ? (
                <EmailViewer
                  email={
                    {
                      ...data,
                      ...(data?.detail || {}),
                      body: data?.detail?.content || data?.detail?.body || '',
                    } as any
                  }
                />
              ) : (
                <>
                  <VoiceMailViewer data={data} />
                  <div className="flex mt-4">
                    <Button type="submit">Assign to Me</Button>
                  </div>
                </>
              )}
            </form>
          )}
        </FormProvider>
      </PopupContent>
    </Popup>
  );
};

export default QueuePreview;
