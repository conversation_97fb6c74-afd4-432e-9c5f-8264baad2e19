import { Button, useCDSS } from '@cdss-modules/design-system';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';

const DUMMY_SAA_ARTICLE = `
<h2>The standard Lorem Ipsum passage, used since the 1500s</h2>
<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.<p>
<h2>Section 1.10.32 of "de Finibus Bonorum et Malorum", written by <PERSON> in 45 BC</h2>
<p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?</p>
`;

const DUMMY_SAA = [
  {
    id: 'dummy-saa-1',
    name: 'How to use SAA?',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    content: DUMMY_SAA_ARTICLE,
  },
  {
    id: 'dummy-saa-2',
    name: 'What is SAA?',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    content: DUMMY_SAA_ARTICLE,
  },
  {
    id: 'dummy-saa-3',
    name: 'Lorem ipsum dolor sit amet',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    content: DUMMY_SAA_ARTICLE,
  },
  {
    id: 'dummy-saa-4',
    name: 'SAA Keyword',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    content: DUMMY_SAA_ARTICLE,
  },
];

export const SAA = () => {
  const { activeSAA, updateActiveSAA } = useCDSS();
  const openedSAA = DUMMY_SAA.find((saa) => saa.id === activeSAA);
  return (
    <div className="relative rounded-t-lg overflow-hidden flex flex-col h-full">
      <div className="bg-primary-600 py-2 px-4 font-bold text-body flex gap-x-4">
        <div className="flex gap-x-2 items-center">
          <Icon
            name="saa"
            size={16}
          />
          SSA:
        </div>
        <div className="relative w-full">
          <Input
            size="s"
            beforeIcon={<Icon name="search" />}
            placeholder="What do you want to know?"
          />
        </div>
      </div>
      <div className="relative w-full h-0 flex-1 px-4 py-4 flex flex-col">
        {activeSAA ? (
          <>
            <div className="flex items-center gap-x-2">
              <Button
                onClick={() => updateActiveSAA('')}
                variant="back"
                beforeIcon={<Icon name="back" />}
                bodyClassName="p-1 min-w-0"
              />
              <h2 className="font-bold text-t6">{openedSAA?.name}</h2>
            </div>
            <div className="relative w-full h-0 flex-1 overflow-auto py-4 saa-article">
              <span
                dangerouslySetInnerHTML={{ __html: openedSAA?.content || '' }}
              />
            </div>
          </>
        ) : (
          <div className="relative w-full h-0 flex-1 overflow-auto">
            {DUMMY_SAA.map((saa) => (
              <button
                key={saa.id}
                onClick={() => updateActiveSAA(saa.id)}
                className="text-left pb-4 mb-4 border-b border-grey-200 hover:text-primary"
              >
                <div className="font-bold">{saa.name}</div>
                <div className="text-grey-500">{saa.description}</div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SAA;
