import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { useMemo, useState } from 'react';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import dayjs from 'dayjs';
import { Reply } from 'lucide-react';
import Input from '@cdss-modules/design-system/components/_ui/Input';

const templateData = [
  {
    pkey: 'test_chinese_template_zh_cn',
    code: 'test_chinese_template_zh_cn',
    dbUser: 'sa',
    name: 'test chinese template',
    category: 'AllowAgent',
    lockCounter: 14,
    messageJson:
      '[{"body":{"text":"想了解其他優惠\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":null},"language":"zh_CN"}]',
    messageParameters: '[]',
    isOffline: true,
    realTemplateId: 'test_chinese_template_zh_cn',
    status: 'ACTIVE',
    createUserCode: 'ansel1',
    createDatetime: '2024-05-06T08:27:47.200Z',
    latestUpdateUserCode: 'SYSTEM',
    latestUpdateDatetime: '2024-05-21T07:56:25.730Z',
    channelType: 'WHATSAPP',
    channelAccountNumber: '***********',
    templateType: 'MEDIA',
    messageCategory: 'MARKETING',
    language: 'zh_CN',
    approvalStatus: 'approved',
    rejectedReason: 'NONE',
    messageJsonHtml:
      '[{"body":{"text":"<p>想了解其他優惠</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":null},"image":{"fileName":null},"language":"zh_CN"}]',
  },
  // {
  //   pkey: 'test_chinese_template_copy__zh_hk',
  //   code: 'test_chinese_template_copy__zh_hk',
  //   dbUser: 'sa',
  //   name: 'test chinese template - (Copy)',
  //   category: 'AllowAgent',
  //   lockCounter: 13,
  //   messageJson:
  //     '[{"body":{"text":"想了解其他優惠\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":null},"language":"zh_HK"}]',
  //   messageParameters: '[]',
  //   isOffline: true,
  //   realTemplateId: 'test_chinese_template_copy__zh_hk',
  //   status: 'ACTIVE',
  //   createUserCode: 'ansel1',
  //   createDatetime: '2024-05-06T08:29:01.950Z',
  //   latestUpdateUserCode: 'SYSTEM',
  //   latestUpdateDatetime: '2024-05-21T07:56:25.734Z',
  //   channelType: 'WHATSAPP',
  //   channelAccountNumber: '***********',
  //   templateType: 'MEDIA',
  //   messageCategory: 'MARKETING',
  //   language: 'zh_HK',
  //   approvalStatus: 'approved',
  //   rejectedReason: 'NONE',
  //   messageJsonHtml:
  //     '[{"body":{"text":"<p>想了解其他優惠</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":null},"image":{"fileName":null},"language":"zh_HK"}]',
  // },
  // {
  //   pkey: 'test_new_cn_template_media_button_0502_en',
  //   code: 'test_new_cn_template_media_button_0502_en',
  //   dbUser: 'sa',
  //   name: 'test new cn template media button 0502',
  //   category: 'AllowAgent',
  //   lockCounter: 33,
  //   messageJson:
  //     '[{"body":{"text":"test\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":null},"language":"en"}]',
  //   messageParameters: '[]',
  //   isOffline: true,
  //   realTemplateId: 'test_new_cn_template_media_button_0502_en',
  //   status: 'ACTIVE',
  //   createUserCode: 'ansel1',
  //   createDatetime: '2024-05-02T10:10:01.222Z',
  //   latestUpdateUserCode: 'SYSTEM',
  //   latestUpdateDatetime: '2024-05-21T07:56:25.737Z',
  //   channelType: 'WHATSAPP',
  //   channelAccountNumber: '***********',
  //   templateType: 'MEDIA',
  //   messageCategory: 'MARKETING',
  //   language: 'en',
  //   approvalStatus: 'approved',
  //   rejectedReason: 'NONE',
  //   messageJsonHtml:
  //     '[{"body":{"text":"<p>test</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
  // },
  {
    pkey: 'test_param_template_allow_agent_send_en',
    code: 'test_param_template_allow_agent_send_en',
    dbUser: 'sa',
    name: 'test param template - allow agent send',
    category: 'AllowAgent',
    lockCounter: 14,
    messageJson:
      '[{"body":{"text":"Hi {{1}}, this is sunmobile test to confirm your purchase of {{2}}\\n"},"option":null,"header":{"text":null},"language":"en"}]',
    messageParameters:
      '[{"id":"1","defaultValue":""},{"id":"2","defaultValue":""}]',
    isOffline: true,
    realTemplateId: 'test_param_template_allow_agent_send_en',
    status: 'ACTIVE',
    createUserCode: 'edmond.chan',
    createDatetime: '2024-05-06T08:13:37.284Z',
    latestUpdateUserCode: 'SYSTEM',
    latestUpdateDatetime: '2024-05-21T07:56:25.773Z',
    channelType: 'WHATSAPP',
    channelAccountNumber: '***********',
    templateType: 'STANDARD',
    messageCategory: 'MARKETING',
    language: 'en',
    approvalStatus: 'approved',
    rejectedReason: 'NONE',
    messageJsonHtml:
      '[{"body":{"text":"<p>Hi {{1}}, this is sunmobile test to confirm your purchase of {{2}}</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
  },
  // {
  //   pkey: 'test_template_name360_0430_en',
  //   code: 'test_template_name360_0430_en',
  //   dbUser: 'sa',
  //   name: 'test template name360 0430',
  //   category: 'AllowAgent',
  //   lockCounter: 35,
  //   messageJson:
  //     '[{"body":{"text":"this is a \\n"},"option":null,"header":{"text":null},"language":"en"}]',
  //   messageParameters: '[]',
  //   isOffline: true,
  //   realTemplateId: 'test_template_name360_0430_en',
  //   status: 'ACTIVE',
  //   createUserCode: 'ansel1',
  //   createDatetime: '2024-04-30T03:29:38.361Z',
  //   latestUpdateUserCode: 'SYSTEM',
  //   latestUpdateDatetime: '2024-05-21T07:56:25.783Z',
  //   channelType: 'WHATSAPP',
  //   channelAccountNumber: '***********',
  //   templateType: 'STANDARD',
  //   messageCategory: 'MARKETING',
  //   language: 'en',
  //   approvalStatus: 'approved',
  //   rejectedReason: 'NONE',
  //   messageJsonHtml:
  //     '[{"body":{"text":"<p>this is a&nbsp;</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
  // },
  // {
  //   pkey: 'testuattemplate0507_en',
  //   code: 'testuattemplate0507_en',
  //   dbUser: 'sa',
  //   name: 'testuattemplate0507',
  //   category: 'AllowAgent',
  //   lockCounter: 2,
  //   messageJson:
  //     '[{"body":{"text":"this is atest\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"}]},"header":{"text":null},"language":"en"}]',
  //   messageParameters: '[]',
  //   isOffline: true,
  //   realTemplateId: 'testuattemplate0507_en',
  //   status: 'ACTIVE',
  //   createUserCode: 'ansel1',
  //   createDatetime: '2024-05-07T09:10:59.234Z',
  //   latestUpdateUserCode: 'SYSTEM',
  //   latestUpdateDatetime: '2024-05-21T07:56:25.753Z',
  //   channelType: 'WHATSAPP',
  //   channelAccountNumber: '***********',
  //   templateType: 'MEDIA',
  //   messageCategory: 'MARKETING',
  //   language: 'en',
  //   approvalStatus: 'approved',
  //   rejectedReason: 'NONE',
  //   messageJsonHtml:
  //     '[{"body":{"text":"<p>this is atest</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"}]},"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
  // },
  // {
  //   pkey: 'uat_id_5_marketing_cn_prc_********__zh_cn',
  //   code: 'uat_id_5_marketing_cn_prc_********__zh_cn',
  //   dbUser: 'sa',
  //   name: 'UAT ID 5 - Marketing CN PRC (********)',
  //   category: 'AllowAgent',
  //   lockCounter: 1,
  //   messageJson:
  //     '[{"body":{"text":"UAT ID 5 - 市场营销 CN PRC (********)\\n"},"option":null,"header":{"text":null},"language":"zh_CN"}]',
  //   messageParameters: '[]',
  //   isOffline: true,
  //   realTemplateId: 'uat_id_5_marketing_cn_prc_********__zh_cn',
  //   status: 'ACTIVE',
  //   createUserCode: 'agent01',
  //   createDatetime: '2024-05-16T07:00:17.010Z',
  //   latestUpdateUserCode: 'SYSTEM',
  //   latestUpdateDatetime: '2024-05-21T07:56:25.768Z',
  //   channelType: 'WHATSAPP',
  //   channelAccountNumber: '***********',
  //   templateType: 'STANDARD',
  //   messageCategory: 'MARKETING',
  //   language: 'zh_CN',
  //   approvalStatus: 'approved',
  //   rejectedReason: 'NONE',
  //   messageJsonHtml:
  //     '[{"body":{"text":"<p>UAT ID 5 - 市场营销 CN PRC (********)</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"zh_CN"}]',
  // },
  // {
  //   pkey: 'uat_id_5_marketing_eng_********__en',
  //   code: 'uat_id_5_marketing_eng_********__en',
  //   dbUser: 'sa',
  //   name: 'UAT ID 5 - Marketing Eng (********)',
  //   category: 'AllowAgent',
  //   lockCounter: 1,
  //   messageJson:
  //     '[{"body":{"text":"UAT ID 5 - Marketing Eng (********)\\n"},"option":null,"header":{"text":null},"language":"en"}]',
  //   messageParameters: '[]',
  //   isOffline: true,
  //   realTemplateId: 'uat_id_5_marketing_eng_********__en',
  //   status: 'ACTIVE',
  //   createUserCode: 'agent01',
  //   createDatetime: '2024-05-16T06:53:21.976Z',
  //   latestUpdateUserCode: 'SYSTEM',
  //   latestUpdateDatetime: '2024-05-21T07:56:25.747Z',
  //   channelType: 'WHATSAPP',
  //   channelAccountNumber: '***********',
  //   templateType: 'STANDARD',
  //   messageCategory: 'MARKETING',
  //   language: 'en',
  //   approvalStatus: 'approved',
  //   rejectedReason: 'NONE',
  //   messageJsonHtml:
  //     '[{"body":{"text":"<p>UAT ID 5 - Marketing Eng (********)</p>\\n"},"option":null,"header":{"text":null},"image":{"fileName":null},"language":"en"}]',
  // },
  {
    pkey: 'uat_id_7_marketing_m_i_cn_hk_********__zh_hk',
    code: 'uat_id_7_marketing_m_i_cn_hk_********__zh_hk',
    dbUser: 'sa',
    name: 'UAT ID 7 - Marketing M&I CN HK (********)',
    category: 'AllowAgent',
    lockCounter: 1,
    messageJson:
      '[{"body":{"text":"UAT ID 7 - Marketing M&amp;I CN HK (********)\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":"UAT ID 7 - Marketing M&I CN HK (********)"},"language":"zh_HK"}]',
    messageParameters: '[]',
    isOffline: true,
    realTemplateId: 'uat_id_7_marketing_m_i_cn_hk_********__zh_hk',
    status: 'ACTIVE',
    createUserCode: 'agent01',
    createDatetime: '2024-05-16T08:11:20.780Z',
    latestUpdateUserCode: 'SYSTEM',
    latestUpdateDatetime: '2024-05-21T07:56:25.723Z',
    channelType: 'WHATSAPP',
    channelAccountNumber: '***********',
    templateType: 'MEDIA',
    messageCategory: 'MARKETING',
    language: 'zh_HK',
    approvalStatus: 'approved',
    rejectedReason: 'NONE',
    messageJsonHtml:
      '[{"body":{"text":"<p>UAT ID 7 - Marketing M&amp;I CN HK (********)</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":"UAT ID 7 - Marketing M&I CN HK (********)"},"image":{"fileName":null},"language":"zh_HK"}]',
  },
  // {
  //   pkey: 'uat_id_7_marketing_m_i_cn_prc_********__zh_cn',
  //   code: 'uat_id_7_marketing_m_i_cn_prc_********__zh_cn',
  //   dbUser: 'sa',
  //   name: 'UAT ID 7 - Marketing M&I CN PRC (********)',
  //   category: 'AllowAgent',
  //   lockCounter: 1,
  //   messageJson:
  //     '[{"body":{"text":"UAT ID 7 - Marketing M&amp;I CN PRC (********)\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":"UAT ID 7 - Marketing M&I CN PRC (********)"},"language":"zh_CN"}]',
  //   messageParameters: '[]',
  //   isOffline: true,
  //   realTemplateId: 'uat_id_7_marketing_m_i_cn_prc_********__zh_cn',
  //   status: 'ACTIVE',
  //   createUserCode: 'agent01',
  //   createDatetime: '2024-05-16T08:06:21.111Z',
  //   latestUpdateUserCode: 'SYSTEM',
  //   latestUpdateDatetime: '2024-05-21T07:56:25.726Z',
  //   channelType: 'WHATSAPP',
  //   channelAccountNumber: '***********',
  //   templateType: 'MEDIA',
  //   messageCategory: 'MARKETING',
  //   language: 'zh_CN',
  //   approvalStatus: 'approved',
  //   rejectedReason: 'NONE',
  //   messageJsonHtml:
  //     '[{"body":{"text":"<p>UAT ID 7 - Marketing M&amp;I CN PRC (********)</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":"UAT ID 7 - Marketing M&I CN PRC (********)"},"image":{"fileName":null},"language":"zh_CN"}]',
  // },
  {
    pkey: 'uat_id_7_marketing_m_i_eng_********__en',
    code: 'uat_id_7_marketing_m_i_eng_********__en',
    dbUser: 'sa',
    name: 'Template with images',
    category: 'AllowAgent',
    lockCounter: 1,
    messageJson:
      '[{"body":{"text":"UAT ID 7 - Marketing M&amp;I Eng (********)\\n"},"image":{"fileName":"https://placehold.co/600x400/EEE/31343C"},option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":"UAT ID 7 - Marketing M&I Eng (********)"},"language":"en"}]',
    messageParameters: '[]',
    isOffline: true,
    realTemplateId: 'uat_id_7_marketing_m_i_eng_********__en',
    status: 'ACTIVE',
    createUserCode: 'agent01',
    createDatetime: '2024-05-16T08:01:04.402Z',
    latestUpdateUserCode: 'SYSTEM',
    latestUpdateDatetime: '2024-05-21T07:56:25.757Z',
    channelType: 'WHATSAPP',
    channelAccountNumber: '***********',
    templateType: 'MEDIA',
    messageCategory: 'MARKETING',
    language: 'en',
    approvalStatus: 'approved',
    rejectedReason: 'NONE',
    messageJsonHtml:
      '[{"body":{"text":"<p>This is a template with image</p>\\n"},"option":{"type":"BUTTON","list":[{"id":0,"type":"QUICK_REPLY","text":"想了解更多呢個優惠詳情","content":"想了解更多呢個優惠詳情"},{"id":1,"type":"QUICK_REPLY","text":"想了解其他優惠","content":"想了解其他優惠"},{"id":2,"type":"QUICK_REPLY","text":"不想再收到此通知訊息","content":"不想再收到此通知訊息"}]},"header":{"text":"With Img"},"image":{"fileName":"https://placehold.co/600x400/EEE/31343C"},"language":"en"}]',
  },
];

function extractVariableIndices(htmlText: string): number[] {
  if (!htmlText) return [];
  // Regular expression to find occurrences of {{number}}
  const regex = /{{(\d+)}}/g;
  let match: RegExpExecArray | null;
  const indices: number[] = [];

  // Continue to apply the regex until no more matches are found
  while ((match = regex.exec(htmlText)) !== null) {
    // Convert the captured group to a number and add to the array
    const index = parseInt(match[1], 10);
    indices.push(index);
  }

  return indices;
}
function displayTemplateText(
  templateText: string,
  watch: (variableName: string) => string
): string {
  // Regular expression to match placeholders like {{0}} to {{9}}
  const regex = /{{(\d+)}}/g;

  // Replace each match in the templateText
  const resultText = templateText.replace(regex, (match, index) => {
    // Call the watch function with 'v' followed by the index found
    const dv = watch(`v${index}`);
    return dv ? dv : match;
  });

  return resultText;
}

export function TemplateSelector() {
  const [template, setTemplate] = useState<string>('');
  const [variables, setVariables] = useState<{
    [key: string]: string;
  }>({});
  const watch = (variableName: string) => {
    return variables?.[variableName];
  };

  const activeTemplate = useMemo(() => {
    return {
      ...JSON.parse(
        templateData?.find((item: any) => item.pkey === template)
          ?.messageJsonHtml ?? '{}'
      ),
      // ...JSON.parse("[{\"body\":{\"text\":\"<p>test {{0}}, {{1}} test {{2}} {{3}} {{4}} test</p>\\n\"},\"option\":{\"type\":\"BUTTON\",\"list\":[{\"id\":0,\"type\":\"QUICK_REPLY\",\"text\":\"想了解更多呢個優惠詳情\",\"content\":\"想了解更多呢個優惠詳情\"},{\"id\":1,\"type\":\"QUICK_REPLY\",\"text\":\"想了解其他優惠\",\"content\":\"想了解其他優惠\"},{\"id\":2,\"type\":\"QUICK_REPLY\",\"text\":\"不想再收到此通知訊息\",\"content\":\"不想再收到此通知訊息\"}]},\"header\":{\"text\":null},\"image\":{\"fileName\":null},\"language\":\"en\"}]"),
    };
  }, [template]);
  const varList = extractVariableIndices(
    activeTemplate?.[0]?.body?.text
  ) as number[];

  return (
    <div className="w-full flex flex-col gap-y-3">
      <Field
        title={<span className="text-remark">Template:</span>}
        icon={<Icon name="error" />}
        status={undefined}
        message=""
      >
        <Select
          placeholder="Select a template"
          mode="single"
          options={
            templateData
              ?.filter((item: any) => item?.status === 'ACTIVE')
              ?.map((item: any) => ({
                id: item.pkey,
                label: item.name,
                value: item.pkey,
              })) ?? []
          }
          showSearch={true}
          status={undefined}
          value={template}
          onChange={(v) => setTemplate(v)}
          labelClassName="h-full text-remark"
          labelContainerClassName="h-8"
        />
      </Field>

      <div className="flex-1 bg-[#ECE5DD] rounded-lg overflow-hidden text-remark">
        <div className="p-4 flex flex-col gap-y-4">
          <div className="relative flex items-start">
            <div className="inline-flex flex-none">
              <Icon name="whatsapp-cornner" />
            </div>
            <div className="relative flex flex-col bg-white rounded-b-lg rounded-tr-lg max-w-[300px]">
              <div className="relative flex flex-col p-2">
                <div className="flex-1">
                  {activeTemplate?.[0]?.image?.fileName && (
                    <img
                      src={activeTemplate?.[0]?.image?.fileName}
                      alt="template"
                      className="w-full h-40 object-cover mb-2"
                    />
                  )}
                  {activeTemplate?.[0]?.header?.text && (
                    <h3 className="text-t6 mb-2 font-bold">
                      {activeTemplate?.[0]?.header?.text}
                    </h3>
                  )}
                  {template ? (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: displayTemplateText(
                          activeTemplate?.[0]?.body?.text ?? '',
                          watch
                        ),
                      }}
                    />
                  ) : (
                    '(Please select a template to see the preview)'
                  )}
                  <div className="inline-flex h-2 w-16" />
                </div>
                <div className="absolute right-2 bottom-2 text-mini text-grey-400 -mb-1">
                  {dayjs().format('hh:mm A')}
                </div>
              </div>
              {activeTemplate?.[0]?.option &&
                activeTemplate?.[0]?.option?.type === 'BUTTON' && (
                  <div className="relative w-full text-left text-footnote">
                    {activeTemplate?.[0]?.option?.list?.map((item: any) => (
                      <div
                        key={`msg-preview-button-${item?.text}`}
                        className="flex justify-center items-center gap-x-2 py-2 px-6 relative w-full text-blue-400 border-t border-grey-400"
                      >
                        <Reply className="size-4 inline-block flex-none" />
                        {item?.text}
                      </div>
                    ))}
                  </div>
                )}
            </div>
          </div>
        </div>
      </div>

      {varList && varList?.length > 0 && (
        <Field
          title={<span className="text-remark">Variables:</span>}
          icon={<Icon name="error" />}
          status={undefined}
        >
          <div className="space-y-2">
            {varList?.map((v) => (
              <div key={`vars-${v}`}>
                <Input
                  size="s"
                  status={'danger'}
                  type="text"
                  placeholder={`Variable {{${v}}}`}
                  allowClear
                  value={variables[v]}
                  onChange={(val: any) => {
                    setVariables((prev) => ({
                      ...prev,
                      [`v${v}`]: val,
                    }));
                  }}
                />
              </div>
            ))}
          </div>
        </Field>
      )}
    </div>
  );
}

export default TemplateSelector;
