import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import { useState } from 'react';
import BasicInfo from '../BasicInfo';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Button from '@cdss-modules/design-system/components/_ui/Button';

const DUMMY_DETAIL = {
  // id: 'E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T',
  conversationId: '-',
  startTime: '2024-03-22T02:57:28.000+0000',
  endTime: '2024-03-22T02:58:08.000+0000',
  mediaUri:
    '/recordings/E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T/play/a5d659f4-c5c7-472e-9a7a-f9dcdafc44cf.mp3',
  duration: 40.187,
  users: null,
  direction: 'Internal',
  mediaType: 'call',
  username: 'MOCK USER',
  dialedNumber: '22001',
  callerNumber: '71002',
} as any;

export const BasicHistory = () => {
  const [openedHistory, setOpenedHistory] = useState('');
  return (
    <div className="relative h-0 flex-1 flex flex-col">
      {openedHistory ? (
        <div className="relative p-2 h-0 flex-1 flex flex-col gap-y-4">
          <Button
            onClick={() => setOpenedHistory('')}
            variant="back"
            beforeIcon={<Icon name="back" />}
            bodyClassName="p-1 min-w-0"
          >
            <span className="font-bold">&nbsp;Back to History</span>
          </Button>
          <div className="overflow-y-auto h-0 flex-1 flex flex-col gap-6">
            <BasicInfo
              data={[
                {
                  label: 'Queue',
                  value: 'Complaint',
                  icon: 'user',
                },
                {
                  label: 'Type',
                  value: 'Call',
                  icon: 'phone',
                },
                {
                  label: 'Date',
                  value: '2024-03-22T02:57:28.000+0000',
                  icon: 'calendar',
                },
                {
                  label: 'Duration',
                  value: '40.187',
                  icon: 'prev-10s',
                },
                {
                  label: 'Media Type',
                  value: 'Call',
                  icon: 'file',
                },
                {
                  label: 'Direction',
                  value: 'Internal',
                  icon: 'inbound',
                },
                {
                  label: 'Media Source',
                  value: 'N/A',
                  icon: 'file',
                },
              ]}
            />
          </div>
        </div>
      ) : (
        <div className="overflow-y-auto h-0 flex-1 flex flex-col gap-6">
          <table>
            {new Array(5).fill(0).map((_, i) => {
              const type =
                i === 2 ? (
                  <Icon
                    name="phone"
                    size={20}
                  />
                ) : (
                  <Icon
                    name="msg"
                    size={20}
                  />
                );
              const queue = i > 2 ? 'Sales' : 'Customer Service';
              return (
                <tbody
                  key={`history-${i}`}
                  onClick={() => setOpenedHistory('history')}
                  className="hover:bg-primary-200 cursor-pointer"
                >
                  <tr className="border-b border-grey-200">
                    <td className="px-4 text-left py-2">{type}</td>
                    <td className="px-4 text-left py-2">
                      {dayjs().format(GLOBAL_DATETIME_FORMAT)}
                    </td>
                    <td className="px-4 text-left py-2">{queue}</td>
                  </tr>
                </tbody>
              );
            })}
          </table>
        </div>
      )}
    </div>
  );
};

export default BasicHistory;
