import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import { useCDSS } from '@cdss-modules/design-system/context/CDSSContext';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {
  Mail,
  UserPlus,
  Voicemail,
  SquareStack,
  Play,
  Pause,
  Eye,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import { useMemo, useState } from 'react';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import QueueListActionMenu from '../QueueListActionMenu';

export type SideBarProps = {
  testId?: string;
  titleI18n: string;
  descI18n: string;
  btnLabelI18n: string;
  onClickButton: () => void;
};
const QueuePanelAccordion = ({ queue }: any) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { updateOpenQueueData, addInteractionFromQueue } = useCDSS();
  const noOfItems = queue?.items?.length || 0;
  const hasNoItems = noOfItems === 0;

  return (
    <div className={cn('w-full')}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'flex justify-between items-center w-full p-2 px-3 border-b border-grey-100 disabled:text-grey-400'
        )}
        disabled={hasNoItems}
      >
        <div className={cn('text-remark font-bold')}>
          {queue.name} ({queue?.items?.length || 0})
        </div>
        <div
          className={cn(
            'border-solid border-t-black border-t-[6px] border-x-transparent border-x-[5px] border-b-0',
            isOpen ? 'transform rotate-180' : 'transform rotate-0',
            hasNoItems && 'hidden'
          )}
        />
      </button>
      <div
        className={cn(
          'overflow-hidden transition-all bg-grey-100',
          !isOpen && 'hidden'
        )}
      >
        {queue?.items?.map((item: any) => {
          return (
            <div
              key={item.id}
              onClick={() => {
                updateOpenQueueData(item);
              }}
              className={cn(
                'text-remark flex justify-between items-center gap-x-2 w-full p-2 px-3 border-b border-grey-100 hover:bg-primary-200'
              )}
            >
              <div className={cn('flex items-center gap-x-2')}>
                {item.type === 'email' ? (
                  <Mail className="fill-primary-500 text-grey-100" />
                ) : (
                  <Voicemail className="text-tertiary-400" />
                )}
                <div className={cn('')}>{item.from}</div>
              </div>
              <div
                className={cn('flex items-center gap-x-2')}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <DropdownMenu>
                  <Tooltip
                    content="Assign"
                    trigger={
                      <DropdownMenuTrigger asChild>
                        <button
                          type="button"
                          className={cn('text-grey-500 hover:text-primary-500')}
                        >
                          <UserPlus className="size-5" />
                        </button>
                      </DropdownMenuTrigger>
                    }
                  />
                  <DropdownMenuContent>
                    <DropdownMenuItem
                      className="text-remark"
                      onClick={() => addInteractionFromQueue(item?.id)}
                    >
                      Assign to me
                    </DropdownMenuItem>

                    <DropdownMenuSub>
                      <DropdownMenuSubTrigger className="text-remark">
                        Assign to...
                      </DropdownMenuSubTrigger>
                      <DropdownMenuPortal>
                        <DropdownMenuSubContent>
                          <div className="px-3">
                            <Input
                              placeholder="Search..."
                              size="s"
                            />
                          </div>
                          <DropdownMenuItem className="text-remark">
                            <button
                              className="w-full text-left"
                              onClick={() => console.log('Template 2')}
                            >
                              Agent 1
                            </button>
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-remark">
                            <button
                              className="w-full text-left"
                              onClick={() => console.log('Template 3')}
                            >
                              Agent 2
                            </button>
                          </DropdownMenuItem>
                        </DropdownMenuSubContent>
                      </DropdownMenuPortal>
                    </DropdownMenuSub>
                    <DropdownMenuSub>
                      <DropdownMenuSubTrigger className="text-remark">
                        Transfer to...
                      </DropdownMenuSubTrigger>
                      <DropdownMenuPortal>
                        <DropdownMenuSubContent>
                          <div className="px-3">
                            <Input
                              placeholder="Search..."
                              size="s"
                            />
                          </div>
                          <DropdownMenuItem className="text-remark">
                            <button
                              className="w-full text-left"
                              onClick={() => console.log('Template 1')}
                            >
                              Customer Service Queue
                            </button>
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-remark">
                            <button
                              className="w-full text-left"
                              onClick={() => console.log('Template 2')}
                            >
                              Technical Support Queue
                            </button>
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-remark">
                            <button
                              className="w-full text-left"
                              onClick={() => console.log('Template 3')}
                            >
                              Sales Queue
                            </button>
                          </DropdownMenuItem>
                        </DropdownMenuSubContent>
                      </DropdownMenuPortal>
                    </DropdownMenuSub>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const QueuePanelBody = () => {
  const [filter, setFilter] = useState<string>('active');
  const [isPlayRecording, setIsPlayRecording] = useState(false);

  const {
    toggleQueuePanel,
    queuePanelExpanded,
    queueData,
    updateOpenQueueData,
    addInteractionFromQueue,
  } = useCDSS();
  const { toPath } = useRouteHandler();

  const [selected, setSelected] = useState<string[]>([]);
  const selectItem = (id: string) => {
    setSelected((prev) => {
      if (prev.includes(id)) {
        return prev.filter((i) => i !== id);
      }
      return [...prev, id];
    });
  };
  const unSelectItem = (id: string) => {
    setSelected((prev) => prev.filter((i) => i !== id));
  };

  const filteredData = useMemo(() => {
    // Filter out items that are not WAITING for status
    const targetPanel = queueData?.find(
      (panel: any) => panel.id === queuePanelExpanded?.id
    );
    return {
      ...targetPanel,
      items: targetPanel?.items?.filter((i: any) => i.status === 'WAITING'),
    };
  }, [queueData, queuePanelExpanded]);

  return (
    <div className={cn('relative w-full flex flex-col')}>
      <button
        type="button"
        onClick={() => {
          toPath('/queue');
        }}
        className={cn(
          'relative flex justify-between items-center w-full text-body font-bold p-2 px-3 border-b shadow-sm group'
        )}
      >
        <div className={cn('flex items-center gap-x-2')}>
          <SquareStack />
          <span>
            {queuePanelExpanded?.name} ({queuePanelExpanded?.items?.length || 0}
            )
          </span>
        </div>
        <div className="transition-all">
          {selected.length > 0 ? (
            <QueueListActionMenu
              data={{}}
              onClickCancel={() => setSelected([])}
              icon={
                <Button
                  size="s"
                  className="font-normal"
                >
                  Selected ({selected.length})
                </Button>
              }
            />
          ) : (
            <button
              type="button"
              className={cn(
                'rounded-full size-5 bg-black text-white flex justify-center items-center group-hover:bg-primary-500'
              )}
              onClick={() => {
                toggleQueuePanel(null);
              }}
            >
              <Icon
                name="close"
                size={10}
              />
            </button>
          )}
        </div>
      </button>
      {filteredData?.items?.map((item: any) => {
        const isItemSelected = selected.includes(item.id);
        return (
          <div
            key={item.id}
            onClick={() => {
              updateOpenQueueData(item);
              // selectItem(item.id);
            }}
            className={cn(
              'text-remark flex justify-between items-center gap-x-2 w-full p-2 px-3 border-b border-grey-100 hover:bg-primary-200',
              isItemSelected ? 'bg-primary-200' : '',
              'group/queueItem'
            )}
          >
            <div className="flex gap-2 w-3/5">
              <div
                className={cn(
                  'flex items-center gap-x-2',
                  isItemSelected ? 'hidden' : 'group-hover/queueItem:hidden'
                )}
              >
                {item.type === 'email' ? (
                  <Mail className="fill-primary-500 text-white size-7" />
                ) : (
                  <Voicemail className="text-tertiary-400 size-7" />
                )}
              </div>
              <div
                className={cn(
                  'justify-center items-center gap-x-2',
                  isItemSelected ? 'flex' : 'hidden group-hover/queueItem:flex'
                )}
              >
                <div
                  className="relative size-7 flex justify-center items-center -right-1"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <Checkbox
                    checked={isItemSelected}
                    onChange={() => {
                      isItemSelected
                        ? unSelectItem(item.id)
                        : selectItem(item.id);
                    }}
                    className="mr-0"
                  />
                </div>
              </div>
              <div className="flex flex-col w-full">
                <div className={cn('text-remark')}>{item.from}</div>
                <div className={cn('truncate font-bold')}>
                  {item?.detail?.subject || item?.detail?.transcript}
                </div>
              </div>
            </div>
            <div
              className={cn('flex flex-none items-center gap-x-2')}
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              {!isPlayRecording
                ? item.type === 'voicemail' && (
                    <button
                      type="button"
                      className={cn('text-grey-500 hover:text-primary-500')}
                      onClick={() => setIsPlayRecording(!isPlayRecording)}
                    >
                      <Play className="size-5" />
                    </button>
                  )
                : item.type === 'voicemail' && (
                    <button
                      type="button"
                      className={cn('text-grey-500 hover:text-primary-500')}
                      onClick={() => setIsPlayRecording(!isPlayRecording)}
                    >
                      <Pause className="size-5" />
                    </button>
                  )}
              {item.type === 'email' && (
                <button
                  type="button"
                  onClick={() => updateOpenQueueData(item)}
                  className={cn('text-grey-500 hover:text-primary-500')}
                >
                  <Eye className="size-5" />
                </button>
              )}
              <DropdownMenu>
                <Tooltip
                  content="Assign"
                  trigger={
                    <DropdownMenuTrigger asChild>
                      <button
                        type="button"
                        className={cn('text-grey-500 hover:text-primary-500')}
                      >
                        <UserPlus className="size-5" />
                      </button>
                    </DropdownMenuTrigger>
                  }
                />
                <DropdownMenuContent>
                  <DropdownMenuItem
                    className="text-remark"
                    onClick={() => {
                      addInteractionFromQueue(item?.id);
                      queuePanelExpanded?.items?.filter(
                        (fil: any) => fil.id !== item?.id
                      );
                    }}
                  >
                    Assign to me
                  </DropdownMenuItem>

                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger className="text-remark">
                      Assign to...
                    </DropdownMenuSubTrigger>
                    <DropdownMenuPortal>
                      <DropdownMenuSubContent>
                        <div className="px-3">
                          <Input
                            placeholder="Search..."
                            size="s"
                          />
                        </div>
                        <DropdownMenuItem className="text-remark">
                          <button
                            className="w-full text-left"
                            onClick={() => console.log('Template 2')}
                          >
                            Agent 1
                          </button>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-remark">
                          <button
                            className="w-full text-left"
                            onClick={() => console.log('Template 3')}
                          >
                            Agent 2
                          </button>
                        </DropdownMenuItem>
                      </DropdownMenuSubContent>
                    </DropdownMenuPortal>
                  </DropdownMenuSub>
                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger className="text-remark">
                      Transfer to...
                    </DropdownMenuSubTrigger>
                    <DropdownMenuPortal>
                      <DropdownMenuSubContent>
                        <div className="px-3">
                          <Input
                            placeholder="Search..."
                            size="s"
                          />
                        </div>
                        <DropdownMenuItem className="text-remark">
                          <button
                            className="w-full text-left"
                            onClick={() => console.log('Template 1')}
                          >
                            Customer Service Queue
                          </button>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-remark">
                          <button
                            className="w-full text-left"
                            onClick={() => console.log('Template 2')}
                          >
                            Technical Support Queue
                          </button>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-remark">
                          <button
                            className="w-full text-left"
                            onClick={() => console.log('Template 3')}
                          >
                            Sales Queue
                          </button>
                        </DropdownMenuItem>
                      </DropdownMenuSubContent>
                    </DropdownMenuPortal>
                  </DropdownMenuSub>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        );
      })}
      {/* {filteredData?.map((queue: any) => {
        return (
          <QueuePanelAccordion
            key={queue.id}
            queue={queue}
          />
        );
      })} */}
    </div>
  );
};

export default QueuePanelBody;
