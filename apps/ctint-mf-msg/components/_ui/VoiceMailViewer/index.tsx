import { LoadingBlock } from '@cdss-modules/design-system';
import { AudioPlayer } from '@cdss-modules/design-system/components/_ui/AudioPlayer';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import { cn } from '@cdss-modules/design-system/lib/utils';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useGlobalAudioPlayer } from 'react-use-audio-player';

type TVoiceMailViewerProps = {
  data?: any;
};

export function VoiceMailViewer({ data }: TVoiceMailViewerProps) {
  const { load, isReady, seek, error: loadMp3Error } = useGlobalAudioPlayer();
  useEffect(() => {
    load('/ctint/mf-watp/audio/test-transcript.mp3');
  }, []);

  const [pos, setPos] = useState(0);
  return (
    <div className="flex flex-col max-h-[70vh]">
      <div className="w-full py-3 flex justify-between border-b border-grey-200">
        <div className="flex flex-col">
          <div>
            <strong>From: </strong>
            {`${data.from}`}
          </div>
        </div>
        <div className="italic">
          {dayjs(data?.date || undefined).format(GLOBAL_DATETIME_FORMAT)}
        </div>
      </div>
      <div className={cn('py-3 h-0 flex-1 flex flex-col email-previewer')}>
        {isReady ? (
          <AudioPlayer
            updatePos={(p) => setPos(p)}
            label={{
              jumpToTime: 'Jump to time',
              go: 'Go',
              speed: 'Speed',
              invalidTime: 'Invalid time',
            }}
          />
        ) : (
          <LoadingBlock />
        )}
      </div>
      <div className={cn('p-3 mt-4 rounded-lg bg-gray-100')}>
        {data?.detail?.transcript}
      </div>
    </div>
  );
}

export default VoiceMailViewer;
