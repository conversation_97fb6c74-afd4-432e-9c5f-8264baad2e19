import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import { useCDSS } from '@cdss-modules/design-system/context/CDSSContext';
import { cn } from '@cdss-modules/design-system/lib/utils';
import dayjs from 'dayjs';
import ProgressCircle from '../ProgressCircle';
import { useMemo } from 'react';
import { useRouteHandler } from '@cdss-modules/design-system';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { SocialIcon } from 'react-social-icons';
import { Mail } from 'lucide-react';
import { basePath } from '../../../lib/appConfig';

export type TCustomerBlockProps = {
  data: any;
  index: number;
};

const CustomerBlock = ({ data }: TCustomerBlockProps) => {
  const { sideBarExpanded, updateActiveInteraction, activeInteraction } =
    useCDSS();
  const { toPath, activePath } = useRouteHandler();
  const customerName = data?.customerName;
  const shortName = data?.customerShortName;
  const isActive = activeInteraction?.id === data.id && activePath === basePath;
  const isHistory = data?.isHistory;

  const lastMsg = useMemo(() => {
    if (data?.type !== 'msg') return '';
    const contentLen = data?.content?.length;
    if (!contentLen) return '';
    return data?.content?.[contentLen - 1]?.text;
  }, [data?.content, data?.type]);
  return (
    <button
      type="button"
      onClick={() => {
        toPath('/');
        updateActiveInteraction(data.id);
      }}
      className={cn(
        'relative flex w-full px-1 py-0 items-center group/msg-side-bar text-footnote',
        sideBarExpanded
          ? 'justify-start border-b border-grey-200 hover:bg-primary-200'
          : 'justify-center',
        isActive && sideBarExpanded && 'bg-primary-200'
      )}
    >
      <div
        className={cn(
          'relative w-full p-2 flex-none rounded-lg aspect-square max-w-16',
          sideBarExpanded ? '' : 'group-hover/msg-side-bar:bg-primary-200',
          isActive && !sideBarExpanded && 'bg-primary-200'
        )}
      >
        <div className="relative p-0 rounded-full">
          <div className="absolute size-full top-0 left-0 z-20">
            <ProgressCircle
              status={isHistory ? 'history' : 'active'}
              time={data?.time}
              showTime={sideBarExpanded && !isHistory && data?.type === 'call'}
            />
          </div>

          {data?.type === 'msg' ||
          data?.type === 'email' ||
          !sideBarExpanded ||
          isHistory ? (
            <div className="relative rounded-full overflow-hidden">
              <Avatar
                text={shortName}
                textClassName="text-body text-black"
                className={cn(
                  'relative size-full flex-none z-10 bg-transparent'
                )}
              />
              <div className="absolute size-full p-1 left-0 top-0 bg-white">
                <div className="size-full bg-tertiary-200 left-0 top-0 rounded-full overflow-hidden scale-y-0" />
              </div>
            </div>
          ) : (
            <div className="relative size-full aspect-square rounded-full overflow-hidden" />
          )}
          <div
            className={cn(
              'absolute right-[6px] top-[6px] -translate-y-1/2 translate-x-1/2 size-5 bg-white drop-shadow-lg rounded-full overflow-hidden z-30 flex items-center justify-center'
            )}
          >
            {data?.type === 'call' && (
              <div className="flex items-center justify-center size-full text-tertiary-600">
                <Icon
                  name="phone"
                  size={12}
                />
              </div>
            )}
            {data?.type === 'msg' && (
              <SocialIcon
                network="whatsapp"
                style={{ width: '120%', height: '120%' }}
              />
            )}
            {data?.type === 'email' && (
              <Mail className="fill-primary-500 text-white" />
            )}
          </div>
        </div>
      </div>
      <div
        className={cn(
          'flex flex-col items-start gap-y-[2px] flex-1 w-0 py-2 pl-1 pr-2',
          sideBarExpanded
            ? 'transition-all delay-200'
            : 'w-0 h-0 absolute overflow-hidden opacity-0 pointer-events-none'
        )}
      >
        <div className="flex w-full pr-8">
          <div className="font-bold overflow-hidden truncate">
            {customerName}
          </div>
        </div>
        {(data?.type === 'msg' || data?.type === 'email' || isHistory) && (
          <div className="absolute right-4 top-4 text-mini text-grey-500">
            {dayjs(data?.time).format(isHistory ? 'DD/MM' : 'HH:mm')}
          </div>
        )}
        <div className="w-full text-left overflow-hidden truncate whitespace-pre">
          {data?.type === 'email' && <>{data?.emailDetail?.subject}</>}
          {data?.type === 'msg' && <>{lastMsg}</>}
          {data?.type === 'call' && <>{data?.phone}</>}
        </div>
        {data?.type === 'call' && !isHistory && (
          <div className="absolute right-4 top-0 h-full flex items-center justify-center">
            <CallControlButton
              tooltip={'Disconnect'}
              tooltipPosition="right"
              onClick={() => null}
              icon={
                <Icon
                  name="phone-end"
                  size={20}
                />
              }
              className={cn(
                'flex-none size-8 bg-status-danger hover:border-status-danger hover:bg-status-danger hover:opacity-70 text-white disabled:bg-grey-100 disabled:text-grey-300'
              )}
              // active={activeModal === 'dialpad-panel'}
              handleOnChange={() => null}
            />
          </div>
        )}
      </div>
    </button>
  );
};

export default CustomerBlock;
