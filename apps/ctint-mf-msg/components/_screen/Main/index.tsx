import { Panel, useCDSS } from '@cdss-modules/design-system';
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@cdss-modules/design-system/components/_ui/Resizable';
import Chatroom from '../../_ui/Chatroom';
import SAA from '../../_ui/SAA';
import InfoPanel from '../../_ui/InfoPanel';
import Callroom from '../../_ui/Callroom';
import EmailViewer from '../../_ui/EmailViewer';
export function Main() {
  const { activeInteraction } = useCDSS();
  return (
    <ResizablePanelGroup
      direction="horizontal"
      className="flex flex-row gap-x-3 h-full"
    >
      <ResizablePanel
        defaultSize={60}
        minSize={25}
        maxSize={100}
        className="toolbar-panel"
      >
        <ResizablePanelGroup
          direction="vertical"
          className="flex flex-row gap-y-3 h-full"
        >
          <ResizablePanel
            defaultSize={60}
            minSize={25}
            maxSize={100}
            className="toolbar-panel"
          >
            <Panel
              className="p-0"
              containerClassName="h-full"
            >
              <InfoPanel />
            </Panel>
          </ResizablePanel>
          <ResizableHandle />
          <ResizablePanel className="flex flex-col gap-4">
            <Panel
              className="p-0"
              containerClassName="h-full"
            >
              <SAA />
            </Panel>
          </ResizablePanel>
        </ResizablePanelGroup>
      </ResizablePanel>
      <ResizableHandle />
      <ResizablePanel className="flex flex-col gap-4">
        {activeInteraction?.type === 'msg' && <Chatroom />}
        {activeInteraction?.type === 'call' && <Callroom />}
        {activeInteraction?.type === 'email' && (
          <Panel
            className="px-4 pb-4"
            containerClassName="h-full flex flex-col"
          >
            <EmailViewer
              fullHeight
              email={activeInteraction?.emailDetail}
            />
          </Panel>
        )}
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}

export default Main;
