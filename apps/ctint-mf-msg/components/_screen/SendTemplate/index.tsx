import { Button } from '@cdss-modules/design-system';
import TemplateSelector from '../../_ui/TemplateSelector';
import { useEffect, useState } from 'react';
import { CircleCheck } from 'lucide-react';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { DUMMY_CHANNELS } from '../Open';
export function SendTemplate() {
  const [sent, setSent] = useState(false);
  const [channel, setChannel] = useState('');
  useEffect(() => {
    if (sent) {
      setTimeout(() => {
        setSent(false);
      }, 2000);
    }
  }, [sent]);
  return (
    <div className="relative bg-white flex size-full overflow-hidden">
      <div className="bg-white flex flex-col gap-y-3 p-3 h-full overflow-auto">
        <h1 className="text-2xl font-bold">Send a template message</h1>
        <Field
          title={<span className="text-remark">Channel:</span>}
          icon={<Icon name="error" />}
          className="hidden"
          // status={errors?.channel ? 'danger' : undefined}
          // message={errors.channel}
        >
          <Select
            placeholder="Select a channel"
            mode="single"
            labelClassName="h-full text-remark"
            labelContainerClassName="h-8"
            options={DUMMY_CHANNELS}
            showSearch={true}
            value={channel}
            onChange={(v) => setChannel(v)}
          />
        </Field>
        <TemplateSelector />
        <Button
          fullWidth
          size="s"
          onClick={() => {
            if (sent) return;
            setSent(true);
          }}
          beforeIcon={
            sent ? (
              <CircleCheck
                size={16}
                className="!text-white mr-1"
              />
            ) : undefined
          }
          disabled={sent}
          className="!opacity-100"
          bodyClassName={sent ? 'bg-status-success opacity-100' : undefined}
        >
          {sent ? 'Message Sent' : 'Send Now'}
        </Button>
      </div>
    </div>
  );
}

export default SendTemplate;
